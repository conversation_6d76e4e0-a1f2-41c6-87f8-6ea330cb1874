<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

$message = '';
$error = '';

// Create payment_settings table if it doesn't exist
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'payment_settings'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $sql = "CREATE TABLE payment_settings (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        
        // Insert default settings
        $defaultSettings = [
            'paypal_enabled' => '0',
            'paypal_client_id' => '',
            'paypal_client_secret' => '',
            'paypal_sandbox_mode' => '1',
            'stripe_enabled' => '0',
            'stripe_publishable_key' => '',
            'stripe_secret_key' => '',
            'stripe_webhook_secret' => '',
            'stripe_test_mode' => '1',
            'donations_enabled' => '1',
            'birthday_gifts_enabled' => '1',
            'minimum_donation_amount' => '5',
            'default_currency' => 'USD',
            'donation_success_message' => 'Thank you for your generous donation!',
            'donation_email_template' => 'Thank you for your donation of {amount} {currency}. Your support helps us continue our mission.',
            'payment_notification_email' => ''
        ];
        
        foreach ($defaultSettings as $key => $value) {
            updatePaymentSetting($conn, $key, $value);
        }
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Error creating payment_settings table: " . $e->getMessage());
}

// Function to update payment settings
function updatePaymentSetting($conn, $key, $value) {
    try {
        $stmt = $conn->prepare("INSERT INTO payment_settings (setting_key, setting_value) 
                               VALUES (?, ?) 
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $stmt->execute([$key, $value]);
    } catch (PDOException $e) {
        error_log("Error updating payment setting: " . $e->getMessage());
        throw $e;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // PayPal Settings
        if (isset($_POST['paypal_settings'])) {
            $paypal_enabled = isset($_POST['paypal_enabled']) ? '1' : '0';
            $paypal_client_id = trim($_POST['paypal_client_id']);
            $paypal_client_secret = trim($_POST['paypal_client_secret']);
            $paypal_sandbox_mode = isset($_POST['paypal_sandbox_mode']) ? '1' : '0';
            
            updatePaymentSetting($conn, 'paypal_enabled', $paypal_enabled);
            updatePaymentSetting($conn, 'paypal_client_id', $paypal_client_id);
            updatePaymentSetting($conn, 'paypal_client_secret', $paypal_client_secret);
            updatePaymentSetting($conn, 'paypal_sandbox_mode', $paypal_sandbox_mode);
            
            $message = "PayPal settings updated successfully.";
        }
        
        // Stripe Settings
        if (isset($_POST['stripe_settings'])) {
            $stripe_enabled = isset($_POST['stripe_enabled']) ? '1' : '0';
            $stripe_publishable_key = trim($_POST['stripe_publishable_key']);
            $stripe_secret_key = trim($_POST['stripe_secret_key']);
            $stripe_webhook_secret = trim($_POST['stripe_webhook_secret']);
            $stripe_test_mode = isset($_POST['stripe_test_mode']) ? '1' : '0';
            
            updatePaymentSetting($conn, 'stripe_enabled', $stripe_enabled);
            updatePaymentSetting($conn, 'stripe_publishable_key', $stripe_publishable_key);
            updatePaymentSetting($conn, 'stripe_secret_key', $stripe_secret_key);
            updatePaymentSetting($conn, 'stripe_webhook_secret', $stripe_webhook_secret);
            updatePaymentSetting($conn, 'stripe_test_mode', $stripe_test_mode);
            
            $message = "Stripe settings updated successfully.";
        }
        
        // General Payment Settings
        if (isset($_POST['general_settings'])) {
            $donations_enabled = isset($_POST['donations_enabled']) ? '1' : '0';
            $birthday_gifts_enabled = isset($_POST['birthday_gifts_enabled']) ? '1' : '0';
            $minimum_donation_amount = max(1, floatval($_POST['minimum_donation_amount']));
            $default_currency = trim($_POST['default_currency']);
            $donation_success_message = trim($_POST['donation_success_message']);
            $donation_email_template = trim($_POST['donation_email_template']);
            $payment_notification_email = trim($_POST['payment_notification_email']);
            
            updatePaymentSetting($conn, 'donations_enabled', $donations_enabled);
            updatePaymentSetting($conn, 'birthday_gifts_enabled', $birthday_gifts_enabled);
            updatePaymentSetting($conn, 'minimum_donation_amount', $minimum_donation_amount);
            updatePaymentSetting($conn, 'default_currency', $default_currency);
            updatePaymentSetting($conn, 'donation_success_message', $donation_success_message);
            updatePaymentSetting($conn, 'donation_email_template', $donation_email_template);
            updatePaymentSetting($conn, 'payment_notification_email', $payment_notification_email);
            
            $message = "General payment settings updated successfully.";
        }
        
    } catch (PDOException $e) {
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Load current settings
try {
    $stmt = $conn->prepare("SELECT * FROM payment_settings");
    $stmt->execute();
    $payment_settings = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $payment_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    $error = "Error loading settings: " . $e->getMessage();
}

// Currency options
$currencies = [
    'USD' => 'US Dollar',
    'EUR' => 'Euro',
    'GBP' => 'British Pound',
    'ZAR' => 'South African Rand',
    'NGN' => 'Nigerian Naira',
    'KES' => 'Kenyan Shilling',
    'UGX' => 'Ugandan Shilling',
    'GHS' => 'Ghanaian Cedi'
];

// Set page title and header
$page_title = "Payment Integration";
$page_header = "Payment Integration Settings";
$page_description = "Configure payment gateways and donation settings for your church website.";

// Include header
include 'includes/header.php';
?>
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show"><?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show"><?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <!-- General Payment Settings -->
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>General Payment Settings</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <input type="hidden" name="general_settings" value="1">
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="donations_enabled" name="donations_enabled" <?php echo $payment_settings['donations_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="donations_enabled">Enable Online Donations</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="birthday_gifts_enabled" name="birthday_gifts_enabled" <?php echo $payment_settings['birthday_gifts_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="birthday_gifts_enabled">Enable Birthday Gift Donations</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="minimum_donation_amount" class="form-label">Minimum Donation Amount</label>
                                    <input type="number" class="form-control" id="minimum_donation_amount" name="minimum_donation_amount" value="<?php echo htmlspecialchars($payment_settings['minimum_donation_amount']); ?>" min="1" step="0.01">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="default_currency" class="form-label">Default Currency</label>
                                    <select class="form-select" id="default_currency" name="default_currency">
                                        <?php foreach ($currencies as $code => $name): ?>
                                            <option value="<?php echo $code; ?>" <?php echo $payment_settings['default_currency'] === $code ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars("$code - $name"); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="donation_success_message" class="form-label">Donation Success Message</label>
                                    <textarea class="form-control" id="donation_success_message" name="donation_success_message" rows="2"><?php echo htmlspecialchars($payment_settings['donation_success_message']); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="donation_email_template" class="form-label">Donation Email Template</label>
                                    <textarea class="form-control" id="donation_email_template" name="donation_email_template" rows="3"><?php echo htmlspecialchars($payment_settings['donation_email_template']); ?></textarea>
                                    <small class="text-muted">Available variables: {amount}, {currency}, {date}, {donor_name}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="payment_notification_email" class="form-label">Payment Notification Email</label>
                                    <input type="email" class="form-control" id="payment_notification_email" name="payment_notification_email" value="<?php echo htmlspecialchars($payment_settings['payment_notification_email']); ?>">
                                    <small class="text-muted">Email address to receive payment notifications</small>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Save General Settings</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- PayPal Integration -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>PayPal Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <input type="hidden" name="paypal_settings" value="1">
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="paypal_enabled" name="paypal_enabled" <?php echo $payment_settings['paypal_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="paypal_enabled">Enable PayPal Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="paypal_client_id" class="form-label">Client ID</label>
                                    <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" value="<?php echo htmlspecialchars($payment_settings['paypal_client_id']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="paypal_client_secret" class="form-label">Client Secret</label>
                                    <input type="password" class="form-control" id="paypal_client_secret" name="paypal_client_secret" value="<?php echo htmlspecialchars($payment_settings['paypal_client_secret']); ?>">
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="paypal_sandbox_mode" name="paypal_sandbox_mode" <?php echo $payment_settings['paypal_sandbox_mode'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="paypal_sandbox_mode">Enable Sandbox Mode</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Save PayPal Settings</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Stripe Integration -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Stripe Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <input type="hidden" name="stripe_settings" value="1">
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="stripe_enabled" name="stripe_enabled" <?php echo $payment_settings['stripe_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="stripe_enabled">Enable Stripe Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="stripe_publishable_key" class="form-label">Publishable Key</label>
                                    <input type="text" class="form-control" id="stripe_publishable_key" name="stripe_publishable_key" value="<?php echo htmlspecialchars($payment_settings['stripe_publishable_key']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="stripe_secret_key" class="form-label">Secret Key</label>
                                    <input type="password" class="form-control" id="stripe_secret_key" name="stripe_secret_key" value="<?php echo htmlspecialchars($payment_settings['stripe_secret_key']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="stripe_webhook_secret" class="form-label">Webhook Secret</label>
                                    <input type="password" class="form-control" id="stripe_webhook_secret" name="stripe_webhook_secret" value="<?php echo htmlspecialchars($payment_settings['stripe_webhook_secret']); ?>">
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="stripe_test_mode" name="stripe_test_mode" <?php echo $payment_settings['stripe_test_mode'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="stripe_test_mode">Enable Test Mode</label>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">Save Stripe Settings</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


<?php include 'includes/footer.php'; ?> 
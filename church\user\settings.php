<?php
/**
 * User Settings & Preferences
 * 
 * Allows authenticated users to manage their account settings and preferences
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

$error = '';
$success = '';

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Check if user must change password (redirect to change password page)
if ($userData['must_change_password']) {
    header("Location: change_password.php");
    exit();
}

// Get user preferences
$preferences = $userAuth->getUserPreferences($userId);

// Set default preferences if not set
$defaultPreferences = [
    'email_notifications' => true,
    'sms_notifications' => false,
    'birthday_reminders' => true,
    'event_notifications' => true,
    'newsletter_subscription' => true,
    'profile_visibility' => 'members',
    'timezone' => 'America/New_York',
    'language' => 'en',
    'dashboard_layout' => 'default'
];

foreach ($defaultPreferences as $key => $value) {
    if (!isset($preferences[$key])) {
        $preferences[$key] = $value;
    }
}

// Process settings update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !$security->validateCSRFToken($_POST['csrf_token'])) {
        $error = "Invalid form submission. Please try again.";
    } else {
        $settingsUpdated = 0;
        
        // Update notification preferences
        $notificationSettings = [
            'email_notifications' => isset($_POST['email_notifications']),
            'sms_notifications' => isset($_POST['sms_notifications']),
            'birthday_reminders' => isset($_POST['birthday_reminders']),
            'event_notifications' => isset($_POST['event_notifications']),
            'newsletter_subscription' => isset($_POST['newsletter_subscription'])
        ];
        
        foreach ($notificationSettings as $key => $value) {
            if ($userAuth->setUserPreference($userId, $key, $value, 'boolean')) {
                $settingsUpdated++;
            }
        }
        
        // Update privacy settings
        $profileVisibility = $_POST['profile_visibility'] ?? 'members';
        if (in_array($profileVisibility, ['public', 'members', 'private'])) {
            if ($userAuth->setUserPreference($userId, 'profile_visibility', $profileVisibility, 'string')) {
                $settingsUpdated++;
            }
        }
        
        // Update display preferences
        $timezone = $_POST['timezone'] ?? 'America/New_York';
        $language = $_POST['language'] ?? 'en';
        $dashboardLayout = $_POST['dashboard_layout'] ?? 'default';
        
        if ($userAuth->setUserPreference($userId, 'timezone', $timezone, 'string')) {
            $settingsUpdated++;
        }
        
        if ($userAuth->setUserPreference($userId, 'language', $language, 'string')) {
            $settingsUpdated++;
        }
        
        if ($userAuth->setUserPreference($userId, 'dashboard_layout', $dashboardLayout, 'string')) {
            $settingsUpdated++;
        }
        
        if ($settingsUpdated > 0) {
            $success = "Settings updated successfully!";
            // Refresh preferences
            $preferences = $userAuth->getUserPreferences($userId);
        } else {
            $error = "No changes were made to your settings.";
        }
    }
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .settings-container {
            margin-top: 2rem;
        }
        
        .settings-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }

        .settings-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .form-control, .form-select {
            border-radius: var(--bs-border-radius, 10px);
            border: 2px solid var(--bs-border-color, #e9ecef);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--bs-primary, #667eea);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background-color: #fee;
            color: #c33;
        }
        
        .alert-success {
            background-color: #efe;
            color: #363;
        }
        
        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .form-check {
            margin-bottom: 1rem;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .form-check-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }
        
        .settings-section {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 2rem;
            margin-bottom: 2rem;
        }
        
        .settings-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .settings-section h5 {
            color: #2c3e50;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .settings-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }
        
        .privacy-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .privacy-public {
            background-color: #d4edda;
            color: #155724;
        }
        
        .privacy-members {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .privacy-private {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <?php
                // Use the existing logo management system
                $headerLogo = get_site_setting('header_logo', '');
                $mainLogo = get_site_setting('main_logo', '');
                $logoToUse = !empty($headerLogo) ? $headerLogo : $mainLogo;
                if (!empty($logoToUse)): ?>
                    <img src="<?php echo get_base_url() . '/' . htmlspecialchars($logoToUse); ?>"
                         alt="<?php echo get_organization_name(); ?>"
                         class="navbar-logo me-2">
                    <?php echo htmlspecialchars(get_organization_name()); ?>
                <?php else: ?>
                    <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
                <?php endif; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Empty left side to push everything to the right -->
                <div class="navbar-nav me-auto"></div>

                <!-- Main navigation items on the right -->
                <ul class="navbar-nav me-3">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>

                <!-- User dropdown -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container settings-container">
        <!-- Settings Header -->
        <div class="settings-header">
            <h1><i class="bi bi-gear"></i> Account Settings</h1>
            <p class="mb-0">Manage your preferences and account settings</p>
        </div>

        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-lg-8">
                <div class="settings-card">
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                        <?php echo $security->generateCSRFInput(); ?>
                        
                        <!-- Notification Settings -->
                        <div class="settings-section">
                            <h5><i class="bi bi-bell"></i> Notification Preferences</h5>
                            <p class="settings-description">Choose how you want to receive notifications and updates.</p>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                       <?php echo $preferences['email_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications">
                                    <strong>Email Notifications</strong><br>
                                    <small class="text-muted">Receive important updates and announcements via email</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" 
                                       <?php echo $preferences['sms_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_notifications">
                                    <strong>SMS Notifications</strong><br>
                                    <small class="text-muted">Receive urgent notifications via text message</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="birthday_reminders" name="birthday_reminders" 
                                       <?php echo $preferences['birthday_reminders'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="birthday_reminders">
                                    <strong>Birthday Reminders</strong><br>
                                    <small class="text-muted">Get notified about upcoming birthdays in the community</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_notifications" name="event_notifications" 
                                       <?php echo $preferences['event_notifications'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_notifications">
                                    <strong>Event Notifications</strong><br>
                                    <small class="text-muted">Receive updates about church events and activities</small>
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter_subscription" name="newsletter_subscription" 
                                       <?php echo $preferences['newsletter_subscription'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="newsletter_subscription">
                                    <strong>Newsletter Subscription</strong><br>
                                    <small class="text-muted">Subscribe to our regular newsletter and updates</small>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Privacy Settings -->
                        <div class="settings-section">
                            <h5><i class="bi bi-shield-lock"></i> Privacy Settings</h5>
                            <p class="settings-description">Control who can see your profile information.</p>
                            
                            <div class="mb-3">
                                <label for="profile_visibility" class="form-label">Profile Visibility</label>
                                <select class="form-select" id="profile_visibility" name="profile_visibility">
                                    <option value="public" <?php echo ($preferences['profile_visibility'] ?? 'members') == 'public' ? 'selected' : ''; ?>>
                                        Public - Anyone can see your profile
                                    </option>
                                    <option value="members" <?php echo ($preferences['profile_visibility'] ?? 'members') == 'members' ? 'selected' : ''; ?>>
                                        Members Only - Only church members can see your profile
                                    </option>
                                    <option value="private" <?php echo ($preferences['profile_visibility'] ?? 'members') == 'private' ? 'selected' : ''; ?>>
                                        Private - Only you and administrators can see your profile
                                    </option>
                                </select>
                                <div class="mt-2">
                                    Current setting: 
                                    <span class="privacy-badge privacy-<?php echo $preferences['profile_visibility'] ?? 'members'; ?>">
                                        <?php echo ucfirst($preferences['profile_visibility'] ?? 'members'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Display Preferences -->
                        <div class="settings-section">
                            <h5><i class="bi bi-palette"></i> Display Preferences</h5>
                            <p class="settings-description">Customize how the system appears to you.</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="timezone" class="form-label">Timezone</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="America/New_York" <?php echo ($preferences['timezone'] ?? 'America/New_York') == 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                            <option value="America/Chicago" <?php echo ($preferences['timezone'] ?? 'America/New_York') == 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                            <option value="America/Denver" <?php echo ($preferences['timezone'] ?? 'America/New_York') == 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                            <option value="America/Los_Angeles" <?php echo ($preferences['timezone'] ?? 'America/New_York') == 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">Language</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="en" <?php echo ($preferences['language'] ?? 'en') == 'en' ? 'selected' : ''; ?>>English</option>
                                            <option value="es" <?php echo ($preferences['language'] ?? 'en') == 'es' ? 'selected' : ''; ?>>Español</option>
                                            <option value="fr" <?php echo ($preferences['language'] ?? 'en') == 'fr' ? 'selected' : ''; ?>>Français</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dashboard_layout" class="form-label">Dashboard Layout</label>
                                <select class="form-select" id="dashboard_layout" name="dashboard_layout">
                                    <option value="default" <?php echo ($preferences['dashboard_layout'] ?? 'default') == 'default' ? 'selected' : ''; ?>>Default Layout</option>
                                    <option value="compact" <?php echo ($preferences['dashboard_layout'] ?? 'default') == 'compact' ? 'selected' : ''; ?>>Compact Layout</option>
                                    <option value="detailed" <?php echo ($preferences['dashboard_layout'] ?? 'default') == 'detailed' ? 'selected' : ''; ?>>Detailed Layout</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" name="update_settings" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Save Settings
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Settings Info -->
            <div class="col-lg-4">
                <div class="settings-card">
                    <h5 class="mb-3"><i class="bi bi-info-circle"></i> Settings Help</h5>
                    
                    <div class="mb-3">
                        <h6><i class="bi bi-bell"></i> Notifications</h6>
                        <p class="small text-muted">Control how and when you receive updates from the system. You can always change these settings later.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="bi bi-shield-lock"></i> Privacy</h6>
                        <p class="small text-muted">Your privacy is important. Choose who can see your profile information and contact details.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="bi bi-palette"></i> Display</h6>
                        <p class="small text-muted">Customize the appearance and behavior of your dashboard and interface.</p>
                    </div>
                </div>
                
                <div class="settings-card">
                    <h5 class="mb-3"><i class="bi bi-question-circle"></i> Need Help?</h5>
                    <p class="small text-muted">If you have questions about these settings or need assistance, please contact our support team.</p>
                    <div class="d-grid">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-envelope"></i> Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (alert.classList.contains('alert-success')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 5000);
        
        // Update privacy badge when selection changes
        document.getElementById('profile_visibility').addEventListener('change', function() {
            const badge = document.querySelector('.privacy-badge');
            const value = this.value;
            
            badge.className = `privacy-badge privacy-${value}`;
            badge.textContent = value.charAt(0).toUpperCase() + value.slice(1);
        });
    </script>
</body>
</html>

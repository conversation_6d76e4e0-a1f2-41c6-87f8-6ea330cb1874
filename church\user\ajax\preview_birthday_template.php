<?php
/**
 * Birthday Template Preview AJAX Endpoint
 * 
 * Returns HTML preview of a birthday template with specific recipient data
 */

// Include configuration
require_once '../../config.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../../classes/SecurityManager.php';
require_once '../../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    http_response_code(401);
    echo 'Unauthorized';
    exit();
}

// Get parameters from request
$templateId = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;
$recipientId = isset($_GET['recipient_id']) ? intval($_GET['recipient_id']) : 0;

if (!$templateId || !$recipientId) {
    http_response_code(400);
    echo 'Invalid parameters';
    exit();
}

try {
    // Get template
    $stmt = $pdo->prepare("
        SELECT id, name, description, template_content, category
        FROM birthday_templates 
        WHERE id = ? AND is_active = 1 AND is_user_selectable = 1
    ");
    $stmt->execute([$templateId]);
    $template = $stmt->fetch();
    
    if (!$template) {
        http_response_code(404);
        echo 'Template not found';
        exit();
    }
    
    // Get recipient
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email
        FROM members 
        WHERE id = ? AND status = 'active' AND id != ?
    ");
    $stmt->execute([$recipientId, $userId]);
    $recipient = $stmt->fetch();
    
    if (!$recipient) {
        http_response_code(404);
        echo 'Recipient not found';
        exit();
    }
    
    // Get site settings for branding
    $sitename = 'Church Management System';
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
    $stmt->execute();
    $siteNameResult = $stmt->fetch();
    if ($siteNameResult) {
        $sitename = $siteNameResult['setting_value'];
    }
    
    // Create preview content with actual recipient data
    $previewContent = $template['template_content'];
    $previewContent = str_replace('{{recipient_name}}', $recipient['first_name'], $previewContent);
    $previewContent = str_replace('{{sender_name}}', $userData['first_name'], $previewContent);
    $previewContent = str_replace('{{church_name}}', $sitename, $previewContent);
    
    // Output the preview
    echo '<div class="template-preview-header mb-3">';
    echo '<h6>' . htmlspecialchars($template['name']) . '</h6>';
    echo '<p class="text-muted small">Preview for: <strong>' . htmlspecialchars($recipient['full_name']) . '</strong></p>';
    echo '<span class="badge bg-secondary">' . ucfirst($template['category']) . '</span>';
    echo '</div>';
    
    echo '<div class="template-preview-content">';
    echo $previewContent;
    echo '</div>';
    
    echo '<div class="template-preview-footer mt-3">';
    echo '<small class="text-muted">This is how the message will appear when sent to ' . htmlspecialchars($recipient['first_name']) . '.</small>';
    echo '</div>';
    
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error loading template preview';
    error_log('Birthday template preview error: ' . $e->getMessage());
}
?>

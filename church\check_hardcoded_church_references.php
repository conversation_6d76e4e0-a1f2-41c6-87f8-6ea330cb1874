<?php
require_once 'config.php';

echo "<h2>Hardcoded Church References Check</h2>";

// Check email templates for hardcoded church references
echo "<h3>1. Email Templates Check</h3>";
try {
    $stmt = $pdo->query("
        SELECT id, template_name, subject, content 
        FROM email_templates 
        WHERE LOWER(subject) LIKE '%church%' 
        OR LOWER(content) LIKE '%church%'
        OR LOWER(template_name) LIKE '%church%'
    ");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($templates) {
        echo "<p style='color: orange;'>⚠ Found " . count($templates) . " email templates with hardcoded 'church' references:</p>";
        
        foreach ($templates as $template) {
            echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 10px;'>";
            echo "<h4>Template: " . htmlspecialchars($template['template_name']) . " (ID: {$template['id']})</h4>";
            
            // Check subject
            if (stripos($template['subject'], 'church') !== false) {
                echo "<p><strong>Subject contains 'church':</strong> " . htmlspecialchars($template['subject']) . "</p>";
            }
            
            // Check content for church references
            $content_lower = strtolower($template['content']);
            if (strpos($content_lower, 'church') !== false) {
                echo "<p><strong>Content contains 'church' references</strong></p>";
                
                // Find specific church references
                preg_match_all('/[^a-zA-Z]church[^a-zA-Z]/i', $template['content'], $matches, PREG_OFFSET_CAPTURE);
                if (!empty($matches[0])) {
                    echo "<p>Found " . count($matches[0]) . " instances:</p>";
                    echo "<ul>";
                    foreach (array_slice($matches[0], 0, 5) as $match) { // Show first 5 instances
                        $start = max(0, $match[1] - 30);
                        $context = substr($template['content'], $start, 80);
                        echo "<li>..." . htmlspecialchars($context) . "...</li>";
                    }
                    echo "</ul>";
                }
            }
            echo "</div>";
        }
    } else {
        echo "<p style='color: green;'>✓ No email templates found with hardcoded 'church' references</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking email templates: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check settings table for church references
echo "<h3>2. Settings Table Check</h3>";
try {
    $stmt = $pdo->query("
        SELECT setting_key, setting_value 
        FROM settings 
        WHERE LOWER(setting_value) LIKE '%church%'
        OR LOWER(setting_key) LIKE '%church%'
    ");
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($settings) {
        echo "<p style='color: orange;'>⚠ Found " . count($settings) . " settings with 'church' references:</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
        foreach ($settings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: green;'>✓ No settings found with hardcoded 'church' references</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check current organization settings
echo "<h3>3. Current Organization Settings</h3>";
try {
    echo "<p><strong>Organization Name:</strong> " . get_organization_name() . "</p>";
    echo "<p><strong>Organization Type:</strong> " . get_organization_type() . "</p>";
    echo "<p><strong>Site Title:</strong> " . get_site_title() . "</p>";
    
    // Check if these functions are working properly
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('organization_name', 'organization_type', 'site_title', 'site_name')");
    $orgSettings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h4>Raw Organization Settings:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Setting Key</th><th>Setting Value</th></tr>";
    foreach ($orgSettings as $setting) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
        echo "<td>" . htmlspecialchars($setting['setting_value']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking organization settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check for hardcoded references in key user-facing files
echo "<h3>4. File Content Check</h3>";
$filesToCheck = [
    'register.php' => 'Registration Page',
    'user/login.php' => 'User Login Page',
    'user/dashboard.php' => 'User Dashboard',
    'admin/login.php' => 'Admin Login Page'
];

foreach ($filesToCheck as $file => $description) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $churchCount = substr_count(strtolower($content), 'church');
        
        if ($churchCount > 0) {
            echo "<p style='color: orange;'>⚠ $description ($file): Found $churchCount instances of 'church'</p>";
            
            // Find specific instances
            preg_match_all('/[^a-zA-Z]church[^a-zA-Z]/i', $content, $matches, PREG_OFFSET_CAPTURE);
            if (!empty($matches[0])) {
                echo "<ul>";
                foreach (array_slice($matches[0], 0, 3) as $match) { // Show first 3 instances
                    $start = max(0, $match[1] - 40);
                    $context = substr($content, $start, 100);
                    echo "<li>..." . htmlspecialchars($context) . "...</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: green;'>✓ $description ($file): No hardcoded 'church' references found</p>";
        }
    } else {
        echo "<p style='color: gray;'>- $description ($file): File not found</p>";
    }
}

echo "<h3>5. Summary</h3>";
echo "<p>This check helps identify where hardcoded 'church' references need to be replaced with dynamic organization name placeholders.</p>";
echo "<p><strong>Recommended Actions:</strong></p>";
echo "<ul>";
echo "<li>Replace hardcoded 'church' text with <code>get_organization_name()</code> or <code>get_organization_type()</code> function calls</li>";
echo "<li>Update email templates to use <code>{organization_name}</code> and <code>{organization_type}</code> placeholders</li>";
echo "<li>Ensure all user-facing text is dynamic and configurable</li>";
echo "</ul>";

?>

<?php
require_once 'config.php';

echo "<h2>Email Configuration Test</h2>";

// Test 1: Check if email settings are loaded
echo "<h3>1. Email Settings Check</h3>";
if (!empty($emailSettings)) {
    echo "<p style='color: green;'>✓ Email settings loaded successfully</p>";
    echo "<pre>";
    foreach ($emailSettings as $key => $value) {
        if ($key === 'smtp_password') {
            echo "$key: " . str_repeat('*', strlen($value)) . "\n";
        } else {
            echo "$key: $value\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p style='color: red;'>❌ Email settings not loaded</p>";
}

// Test 2: Test sendWelcomeEmail function
echo "<h3>2. Welcome Email Function Test</h3>";
$testMemberData = [
    'full_name' => 'Test User',
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => '<EMAIL>',
    'phone_number' => '************',
    'home_address' => '123 Test St',
    'occupation' => 'Tester',
    'image_path' => ''
];

echo "<p>Testing with member data:</p>";
echo "<pre>" . print_r($testMemberData, true) . "</pre>";

// Don't actually send the email, just test the function preparation
try {
    // Test template loading
    $stmt = $pdo->prepare("
        SELECT template_ids 
        FROM automated_emails_settings 
        WHERE email_type = 'welcome'
        LIMIT 1
    ");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($settings && !empty($settings['template_ids'])) {
        echo "<p style='color: green;'>✓ Automated welcome email settings found</p>";
        echo "<p>Template IDs: " . $settings['template_ids'] . "</p>";
        
        $template_ids = explode(',', $settings['template_ids']);
        $selected_id = $template_ids[array_rand($template_ids)];
        
        $stmt = $pdo->prepare("
            SELECT id, template_name, subject, content 
            FROM email_templates 
            WHERE id = ?
        ");
        $stmt->execute([$selected_id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template) {
            echo "<p style='color: green;'>✓ Welcome template found: " . htmlspecialchars($template['template_name']) . "</p>";
            echo "<p>Subject: " . htmlspecialchars($template['subject']) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Template ID $selected_id not found</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No automated welcome email settings, will use fallback</p>";
        
        // Test fallback template
        $stmt = $pdo->prepare("
            SELECT id, template_name, subject, content 
            FROM email_templates 
            WHERE template_name LIKE '%welcome%' 
            ORDER BY RAND() 
            LIMIT 1
        ");
        $stmt->execute();
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template) {
            echo "<p style='color: green;'>✓ Fallback welcome template found: " . htmlspecialchars($template['template_name']) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No welcome templates found, will use default</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing welcome email: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Test basic email function
echo "<h3>3. Basic Email Function Test</h3>";
if (function_exists('sendEmail')) {
    echo "<p style='color: green;'>✓ sendEmail function exists</p>";
    
    // Test email settings validation
    $required_settings = ['smtp_host', 'smtp_auth', 'smtp_username', 'smtp_password', 
                         'smtp_secure', 'smtp_port', 'sender_email', 'sender_name'];
    $missing_settings = array_diff($required_settings, array_keys($emailSettings));
    
    if (empty($missing_settings)) {
        echo "<p style='color: green;'>✓ All required email settings present</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing email settings: " . implode(', ', $missing_settings) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ sendEmail function not found</p>";
}

echo "<h3>4. Organization Settings Test</h3>";
echo "<p>Organization Name: " . get_organization_name() . "</p>";
echo "<p>Organization Type: " . get_organization_type() . "</p>";
echo "<p>Site Title: " . get_site_title() . "</p>";

?>

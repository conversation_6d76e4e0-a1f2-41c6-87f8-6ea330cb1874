<?php
/**
 * API endpoint to get appearance settings for public pages
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include configuration
    require_once '../config.php';
    
    // Get appearance settings
    $settings = [];
    $css_variables = [];
    
    // Color settings
    $colorSettings = [
        'primary_color' => '--bs-primary',
        'secondary_color' => '--bs-secondary',
        'success_color' => '--bs-success',
        'danger_color' => '--bs-danger',
        'warning_color' => '--bs-warning',
        'info_color' => '--bs-info',
        'light_color' => '--bs-light',
        'dark_color' => '--bs-dark',
        'background_color' => '--bs-body-bg',
        'text_color' => '--bs-body-color',
        'link_color' => '--bs-link-color',
        'link_hover_color' => '--bs-link-hover-color'
    ];
    
    foreach ($colorSettings as $setting => $cssVar) {
        $value = get_site_setting($setting, '');
        if ($value) {
            $css_variables[$cssVar] = $value;
        }
    }
    
    // Typography settings
    $fontFamily = get_site_setting('primary_font', 'Inter');
    $fontSize = get_site_setting('font_size_base', '16');
    $lineHeight = get_site_setting('line_height_base', '1.5');
    
    if ($fontFamily) {
        $css_variables['--bs-font-sans-serif'] = "'{$fontFamily}', system-ui, -apple-system, sans-serif";
    }
    if ($fontSize) {
        $css_variables['--bs-body-font-size'] = "{$fontSize}px";
    }
    if ($lineHeight) {
        $css_variables['--bs-body-line-height'] = $lineHeight;
    }
    
    // Layout settings
    $borderRadius = get_site_setting('border_radius', '0.375');
    if ($borderRadius) {
        $css_variables['--bs-border-radius'] = "{$borderRadius}rem";
    }
    
    // Get organization information
    $organization_name = get_organization_name();
    $organization_mission = get_site_setting('organization_mission', '');
    $organization_vision = get_site_setting('organization_vision', '');
    $organization_values = get_site_setting('organization_values', '');

    // Get contact information
    $contact_info = [
        'phone' => get_site_setting('contact_phone', ''),
        'email' => get_site_setting('contact_email', ''),
        'address' => get_site_setting('contact_address', ''),
        'city' => get_site_setting('contact_city', ''),
        'state' => get_site_setting('contact_state', ''),
        'zip' => get_site_setting('contact_zip', ''),
        'country' => get_site_setting('contact_country', ''),
        'office_hours' => get_site_setting('office_hours', ''),
        'emergency_contact' => get_site_setting('emergency_contact', '')
    ];

    // Get social media links
    $social_media = [
        'facebook' => get_site_setting('facebook_url', ''),
        'twitter' => get_site_setting('twitter_url', ''),
        'instagram' => get_site_setting('instagram_url', ''),
        'youtube' => get_site_setting('youtube_url', ''),
        'linkedin' => get_site_setting('linkedin_url', ''),
        'website' => get_site_setting('website_url', '')
    ];

    // Get logo URLs if available
    $logo_url = '';
    $header_logo_url = '';
    $favicon_url = '';

    // Check for main logo
    $main_logo_path = get_site_setting('main_logo', '');
    if ($main_logo_path && file_exists('../' . $main_logo_path)) {
        $logo_url = $main_logo_path;
    }

    // Check for header logo (preferred for navbar)
    $header_logo_path = get_site_setting('header_logo', '');
    if ($header_logo_path && file_exists('../' . $header_logo_path)) {
        $header_logo_url = $header_logo_path;
        // Use header logo as primary if available
        $logo_url = $header_logo_path;
    }

    // Check for favicon
    $favicon_ico_path = get_site_setting('favicon_ico', '');
    if ($favicon_ico_path && file_exists('../' . $favicon_ico_path)) {
        $favicon_url = $favicon_ico_path;
    } else {
        // Fallback to PNG favicon
        $favicon_32_path = get_site_setting('favicon_32', '');
        if ($favicon_32_path && file_exists('../' . $favicon_32_path)) {
            $favicon_url = $favicon_32_path;
        }
    }
    
    $response = [
        'success' => true,
        'css_variables' => $css_variables,
        'organization_name' => $organization_name,
        'organization_mission' => $organization_mission,
        'organization_vision' => $organization_vision,
        'organization_values' => $organization_values,
        'contact_info' => $contact_info,
        'social_media' => $social_media,
        'logo_url' => $logo_url,
        'favicon_url' => $favicon_url,
        'theme_mode' => get_site_setting('theme_mode', 'light'),
        'custom_css' => get_site_setting('custom_css', '')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load appearance settings',
        'message' => $e->getMessage()
    ]);
}
?>

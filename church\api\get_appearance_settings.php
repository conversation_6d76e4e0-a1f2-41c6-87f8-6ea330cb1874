<?php
/**
 * API endpoint to get appearance settings for public pages
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

try {
    // Include configuration
    require_once '../config.php';
    
    // Get appearance settings
    $settings = [];
    $css_variables = [];
    
    // Color settings
    $colorSettings = [
        'primary_color' => '--bs-primary',
        'secondary_color' => '--bs-secondary',
        'success_color' => '--bs-success',
        'danger_color' => '--bs-danger',
        'warning_color' => '--bs-warning',
        'info_color' => '--bs-info',
        'light_color' => '--bs-light',
        'dark_color' => '--bs-dark',
        'background_color' => '--bs-body-bg',
        'text_color' => '--bs-body-color',
        'link_color' => '--bs-link-color',
        'link_hover_color' => '--bs-link-hover-color'
    ];
    
    foreach ($colorSettings as $setting => $cssVar) {
        $value = get_site_setting($setting, '');
        if ($value) {
            $css_variables[$cssVar] = $value;
        }
    }
    
    // Typography settings
    $fontFamily = get_site_setting('primary_font', 'Inter');
    $fontSize = get_site_setting('font_size_base', '16');
    $lineHeight = get_site_setting('line_height_base', '1.5');
    
    if ($fontFamily) {
        $css_variables['--bs-font-sans-serif'] = "'{$fontFamily}', system-ui, -apple-system, sans-serif";
    }
    if ($fontSize) {
        $css_variables['--bs-body-font-size'] = "{$fontSize}px";
    }
    if ($lineHeight) {
        $css_variables['--bs-body-line-height'] = $lineHeight;
    }
    
    // Layout settings
    $borderRadius = get_site_setting('border_radius', '0.375');
    if ($borderRadius) {
        $css_variables['--bs-border-radius'] = "{$borderRadius}rem";
    }
    
    // Get organization name
    $organization_name = get_organization_name();
    
    // Get logo URL if available
    $logo_url = '';
    $logo_path = get_site_setting('logo_path', '');
    if ($logo_path && file_exists('../' . $logo_path)) {
        $logo_url = $logo_path;
    }
    
    // Get favicon URL if available
    $favicon_url = '';
    $favicon_path = get_site_setting('favicon_path', '');
    if ($favicon_path && file_exists('../' . $favicon_path)) {
        $favicon_url = $favicon_path;
    }
    
    $response = [
        'success' => true,
        'css_variables' => $css_variables,
        'organization_name' => $organization_name,
        'logo_url' => $logo_url,
        'favicon_url' => $favicon_url,
        'theme_mode' => get_site_setting('theme_mode', 'light'),
        'custom_css' => get_site_setting('custom_css', '')
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load appearance settings',
        'message' => $e->getMessage()
    ]);
}
?>

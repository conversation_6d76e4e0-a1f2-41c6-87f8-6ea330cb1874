:root {
  --bs-primary: #fd7e14;
  --bs-secondary: #6c757d;
  --bs-success: #28a745;
  --bs-danger: #dc3545;
  --bs-warning: #ffc107;
  --bs-info: #17a2b8;
  --bs-light: #f8f9fa;
  --bs-dark: #343a40;
  --bs-body-bg: #fff5f0;
  --bs-body-color: #8b4513;
  --bs-link-color: #fd7e14;
  --bs-link-hover-color: #0056b3;
  --sidebar-bg-color: #1173d4;
  --sidebar-text-color: #ffffff;
  --sidebar-hover-color: #007bff;
  --bs-font-sans-serif: 'Inter', system-ui, -apple-system, sans-serif;
  --bs-body-font-size: 16px;
  --bs-body-line-height: 1.5;
  --bs-border-radius: 0.375rem;
  --sidebar-width: 300px;
  --content-spacing: 30px;
}

/* Sidebar Theme Styles */
.sidebar {
  background-color: var(--sidebar-bg-color) !important;
  color: var(--sidebar-text-color) !important;
  width: var(--sidebar-width) !important;
}

.sidebar .nav-link, .sidebar a {
  color: var(--sidebar-text-color) !important;
}

.sidebar .nav-link:hover, .sidebar a:hover {
  background-color: var(--sidebar-hover-color) !important;
  color: #ffffff !important;
}

.main-content {
  margin-left: calc(var(--sidebar-width) + var(--content-spacing)) !important;
  padding: var(--content-spacing) !important;
}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Freedom Assembly Church International - Men's Visionaire</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Load admin appearance settings -->
    <style id="admin-theme-styles">
        /* This will be populated by admin appearance settings */
    </style>

    <!-- Enhanced Visual Styles -->
    <style>
        /* Enhanced Navigation Styles */
        .navbar {
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.3rem;
            transition: transform 0.3s ease;
        }

        .navbar-brand:hover {
            transform: scale(1.05);
        }

        .nav-link-enhanced {
            position: relative;
            padding: 0.75rem 1rem !important;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link-enhanced:hover {
            background: rgba(255,255,255,0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .nav-link-enhanced i {
            transition: transform 0.3s ease;
        }

        .nav-link-enhanced:hover i {
            transform: scale(1.2);
        }

        .btn-enhanced {
            position: relative;
            overflow: hidden;
            border-radius: 25px !important;
            padding: 0.75rem 1.5rem !important;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        /* Hero Section Enhancements */
        .hero-section {
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .hero-content {
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Card Hover Effects */
        .card {
            transition: all 0.3s ease;
            border: none !important;
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--bs-primary, #007bff), var(--bs-secondary, #6c757d));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        /* Icon Animations */
        .card i {
            transition: all 0.3s ease;
        }

        .card:hover i {
            transform: scale(1.1) rotate(5deg);
            color: var(--bs-primary, #007bff) !important;
        }

        /* Floating Animation for Decorative Elements */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .floating-element {
            animation: float 6s ease-in-out infinite;
        }

        /* Pulse Animation for Call-to-Action Buttons */
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255,255,255,0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255,255,255,0); }
            100% { box-shadow: 0 0 0 0 rgba(255,255,255,0); }
        }

        .btn-pulse {
            animation: pulse 2s infinite;
        }

        /* Smooth Scroll Behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Section Fade In Animation */
        .section-animate {
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .section-animate.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            line-height: 1.2;
        }

        .display-3, .display-5 {
            background: linear-gradient(45deg, var(--bs-primary, #007bff), var(--bs-secondary, #6c757d));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Loading Animation for Events */
        .spinner-border {
            animation: spin 1s linear infinite, pulse 2s ease-in-out infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Social Media Icons Enhancement */
        .social-icon {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .social-icon:hover {
            transform: translateY(-5px) scale(1.1);
        }

        .social-icon::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }

        .social-icon:hover::after {
            width: 100%;
            height: 100%;
        }

        /* Responsive Enhancements */
        @media (max-width: 768px) {
            .nav-link-enhanced {
                padding: 0.5rem 0.75rem !important;
            }

            .btn-enhanced {
                padding: 0.5rem 1rem !important;
                font-size: 0.9rem;
            }

            .hero-section {
                padding: 2rem 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%);">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="#" id="navbar-brand">
                <img id="navbar-logo" src="" alt="Logo" class="me-2" style="height: 40px; display: none;">
                <span id="navbar-title">Freedom Assembly Church</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#hero">
                            <i class="bi bi-house-heart-fill me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#mission">
                            <i class="bi bi-bullseye me-1"></i>Mission
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#about">
                            <i class="bi bi-info-circle-fill me-1"></i>About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#events">
                            <i class="bi bi-calendar-event-fill me-1"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link nav-link-enhanced" href="#contact">
                            <i class="bi bi-envelope-heart-fill me-1"></i>Contact
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-enhanced ms-2" href="church/user/login.php">
                            <i class="bi bi-box-arrow-in-right me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-light btn-enhanced ms-2" href="church/index.php">
                            <i class="bi bi-people-fill me-1"></i>Join Us
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="hero-section section-animate" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%); min-height: 100vh; display: flex; align-items: center; color: white; position: relative; overflow: hidden;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content">
                    <h1 class="display-3 fw-bold mb-4 text-white" style="font-family: var(--bs-font-sans-serif, 'Inter', sans-serif); text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        Welcome to Freedom Assembly Church International
                    </h1>
                    <h2 class="h3 mb-4" style="opacity: 0.9; animation: fadeInUp 1s ease-out 0.3s both;">
                        <i class="bi bi-award-fill me-2"></i>Men's Visionaire and Billionaire Platform
                    </h2>
                    <p class="lead mb-5" style="opacity: 0.8; animation: fadeInUp 1s ease-out 0.6s both; font-size: 1.3rem;">
                        <i class="bi bi-eye-fill me-2"></i>Vision: Raising Men To Stand Tall Within The Family And Community.
                    </p>
                    <div class="d-flex gap-3 flex-wrap" style="animation: fadeInUp 1s ease-out 0.9s both;">
                        <a href="church/index.php" class="btn btn-light btn-lg px-4 py-3 btn-enhanced btn-pulse">
                            <i class="bi bi-people-fill me-2"></i>Join Our Brotherhood
                        </a>
                        <a href="#about" class="btn btn-outline-light btn-lg px-4 py-3 btn-enhanced">
                            <i class="bi bi-info-circle-fill me-2"></i>Learn More
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <div class="hero-image-placeholder floating-element" style="background: rgba(255,255,255,0.1); border-radius: var(--bs-border-radius, 20px); padding: 3rem; margin-top: 2rem; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);">
                        <i class="bi bi-church" style="font-size: 8rem; opacity: 0.8; color: #fff; text-shadow: 0 0 20px rgba(255,255,255,0.5);"></i>
                        <p class="mt-3 h5" style="opacity: 0.9; font-weight: 600;">
                            <i class="bi bi-star-fill me-2"></i>Building Strong Christian Leaders
                        </p>
                        <div class="mt-3">
                            <span class="badge bg-light text-dark me-2 px-3 py-2">
                                <i class="bi bi-people me-1"></i>500+ Members
                            </span>
                            <span class="badge bg-light text-dark px-3 py-2">
                                <i class="bi bi-calendar-check me-1"></i>10+ Years
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Enhanced Decorative elements -->
        <div class="floating-element" style="position: absolute; top: 10%; right: 10%; opacity: 0.15; font-size: 10rem; animation-delay: 1s;">
            <i class="bi bi-star-fill" style="color: #ffd700; text-shadow: 0 0 30px rgba(255,215,0,0.5);"></i>
        </div>
        <div class="floating-element" style="position: absolute; bottom: 20%; left: 5%; opacity: 0.15; font-size: 6rem; animation-delay: 2s;">
            <i class="bi bi-heart-fill" style="color: #ff6b6b; text-shadow: 0 0 20px rgba(255,107,107,0.5);"></i>
        </div>
        <div class="floating-element" style="position: absolute; top: 30%; left: 15%; opacity: 0.1; font-size: 4rem; animation-delay: 3s;">
            <i class="bi bi-shield-fill-check" style="color: #4ecdc4;"></i>
        </div>
        <div class="floating-element" style="position: absolute; bottom: 40%; right: 20%; opacity: 0.1; font-size: 5rem; animation-delay: 1.5s;">
            <i class="bi bi-gem" style="color: #a8e6cf;"></i>
        </div>
    </section>

    <!-- Mission Section -->
    <section id="mission" class="py-5 section-animate" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <div class="mb-4">
                        <i class="bi bi-bullseye" style="font-size: 4rem; color: var(--bs-primary, #007bff); opacity: 0.8;"></i>
                    </div>
                    <h2 class="display-5 fw-bold mb-4" style="color: var(--bs-body-color, #212529);">
                        <i class="bi bi-star me-2"></i>Our Mission
                    </h2>
                    <p class="lead" style="color: var(--bs-body-color, #6c757d); font-size: 1.2rem;">
                        Empowering men to become godly leaders in their families, church, and communities through spiritual growth, leadership development, and financial empowerment.
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 20px);">
                        <div class="card-body text-center p-5">
                            <div class="mb-4 position-relative">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-book-fill" style="font-size: 2.5rem; color: var(--bs-primary, #007bff);"></i>
                                </div>
                                <div class="position-absolute top-0 start-100 translate-middle">
                                    <i class="bi bi-heart-fill" style="color: #ff6b6b; font-size: 1.2rem;"></i>
                                </div>
                            </div>
                            <h4 class="card-title mb-3 fw-bold" style="color: var(--bs-body-color, #212529);">
                                <i class="bi bi-cross me-2"></i>Spiritual Growth
                            </h4>
                            <p class="card-text" style="color: var(--bs-body-color, #6c757d); line-height: 1.6;">
                                Empowering men through spiritual development and biblical teachings that transform lives and strengthen faith.
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2">
                                    <i class="bi bi-check-circle me-1"></i>Bible Study
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 20px);">
                        <div class="card-body text-center p-5">
                            <div class="mb-4 position-relative">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-people-fill" style="font-size: 2.5rem; color: var(--bs-success, #198754);"></i>
                                </div>
                                <div class="position-absolute top-0 start-100 translate-middle">
                                    <i class="bi bi-star-fill" style="color: #ffd700; font-size: 1.2rem;"></i>
                                </div>
                            </div>
                            <h4 class="card-title mb-3 fw-bold" style="color: var(--bs-body-color, #212529);">
                                <i class="bi bi-shield-check me-2"></i>Leadership Development
                            </h4>
                            <p class="card-text" style="color: var(--bs-body-color, #6c757d); line-height: 1.6;">
                                Building strong male leaders for families, church, and community through mentorship and practical training.
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-success bg-opacity-10 text-success px-3 py-2">
                                    <i class="bi bi-trophy me-1"></i>Mentorship
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 20px);">
                        <div class="card-body text-center p-5">
                            <div class="mb-4 position-relative">
                                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                                    <i class="bi bi-graph-up-arrow" style="font-size: 2.5rem; color: var(--bs-warning, #ffc107);"></i>
                                </div>
                                <div class="position-absolute top-0 start-100 translate-middle">
                                    <i class="bi bi-gem" style="color: #a8e6cf; font-size: 1.2rem;"></i>
                                </div>
                            </div>
                            <h4 class="card-title mb-3 fw-bold" style="color: var(--bs-body-color, #212529);">
                                <i class="bi bi-currency-dollar me-2"></i>Financial Empowerment
                            </h4>
                            <p class="card-text" style="color: var(--bs-body-color, #6c757d); line-height: 1.6;">
                                Equipping men with skills and knowledge to achieve financial prosperity and stewardship excellence.
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-warning bg-opacity-10 text-warning px-3 py-2">
                                    <i class="bi bi-piggy-bank me-1"></i>Prosperity
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">About Our Ministry</h2>
                    <p class="lead mb-4" style="opacity: 0.9;" id="about-description">
                        The Men's Visionaire and Billionaire platform at Freedom Assembly Church International is dedicated to raising godly men who stand tall in their families and communities.
                    </p>
                    <p class="mb-4" style="opacity: 0.8;">
                        We focus on spiritual growth, leadership development, and financial empowerment to help men fulfill their God-given purpose and potential. Our ministry provides a supportive brotherhood where men can grow together in faith, wisdom, and prosperity.
                    </p>
                    <div class="d-flex gap-4 mb-4">
                        <div class="text-center">
                            <h3 class="fw-bold">500+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Members</p>
                        </div>
                        <div class="text-center">
                            <h3 class="fw-bold">10+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Years</p>
                        </div>
                        <div class="text-center">
                            <h3 class="fw-bold">50+</h3>
                            <p class="mb-0" style="opacity: 0.8;">Leaders</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <div style="background: rgba(255,255,255,0.1); border-radius: var(--bs-border-radius, 15px); padding: 3rem;">
                            <i class="bi bi-award" style="font-size: 6rem; opacity: 0.8;"></i>
                            <h4 class="mt-3">Excellence in Ministry</h4>
                            <p style="opacity: 0.8;">Committed to developing men of character, integrity, and purpose.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Events Section -->
    <section id="events" class="py-5" style="background-color: var(--bs-body-bg, #f8f9fa);">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center mb-5">
                    <h2 class="display-5 fw-bold mb-4" style="color: var(--bs-body-color, #212529);">Upcoming Events</h2>
                    <p class="lead" style="color: var(--bs-body-color, #6c757d);">
                        Join us for inspiring events, workshops, and gatherings designed to strengthen our brotherhood and faith.
                    </p>
                </div>
            </div>
            <div class="row" id="events-container">
                <div class="col-12 text-center">
                    <div class="spinner-border" role="status" style="color: var(--bs-primary, #007bff);">
                        <span class="visually-hidden">Loading events...</span>
                    </div>
                    <p class="mt-3" style="color: var(--bs-body-color, #6c757d);">Loading upcoming events...</p>
                </div>
            </div>
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <a href="church/user/events.php" class="btn btn-primary btn-lg">
                        <i class="bi bi-calendar-event me-2"></i>View All Events
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5" style="background: linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%); color: white;">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-5 fw-bold mb-4">Connect With Us</h2>
                    <p class="lead mb-5" style="opacity: 0.9;">
                        Join our brotherhood and grow spiritually, personally, and financially. Take the first step towards becoming the man God called you to be.
                    </p>
                    <div class="row g-4 mb-5">
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-envelope" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Email Us</h5>
                                <p id="contact-email" style="opacity: 0.8;"><EMAIL></p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-telephone" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Call Us</h5>
                                <p id="contact-phone" style="opacity: 0.8;">+****************</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <i class="bi bi-geo-alt" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="mt-3">Visit Us</h5>
                                <p id="contact-address" style="opacity: 0.8;">123 Church Street<br>City, State 12345</p>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center gap-3 mb-4">
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-facebook" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-instagram" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-twitter" style="font-size: 1.5rem;"></i>
                        </a>
                        <a href="#" class="btn btn-outline-light btn-lg rounded-circle" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
                            <i class="bi bi-youtube" style="font-size: 1.5rem;"></i>
                        </a>
                    </div>
                    <a href="church/index.php" class="btn btn-light btn-lg px-5 py-3">
                        <i class="bi bi-arrow-right me-2"></i>Get Started Today
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4" style="background-color: var(--bs-dark, #212529); color: white;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 Freedom Assembly Church International. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Men's Visionaire and Billionaire Platform</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Load admin appearance settings and apply them
        async function loadAppearanceSettings() {
            try {
                const response = await fetch('church/api/get_appearance_settings.php');
                const settings = await response.json();

                if (settings.success) {
                    // Apply CSS variables
                    const root = document.documentElement;
                    const cssVars = settings.css_variables || {};

                    Object.keys(cssVars).forEach(key => {
                        root.style.setProperty(key, cssVars[key]);
                    });

                    // Load logo if available
                    if (settings.logo_url) {
                        const logoImg = document.getElementById('navbar-logo');
                        logoImg.src = settings.logo_url;
                        logoImg.style.display = 'block';
                    }

                    // Update organization name and content
                    if (settings.organization_name) {
                        document.getElementById('navbar-title').textContent = settings.organization_name;
                        document.title = settings.organization_name + ' - Men\'s Visionaire';

                        // Update hero section
                        const heroTitle = document.querySelector('#hero h1');
                        if (heroTitle) {
                            heroTitle.textContent = `Welcome to ${settings.organization_name}`;
                        }

                        // Update footer
                        const footerText = document.querySelector('footer .container .row .col-md-6 p');
                        if (footerText) {
                            footerText.textContent = `© 2025 ${settings.organization_name}. All rights reserved.`;
                        }
                    }

                    // Update mission and vision content
                    if (settings.organization_mission) {
                        const missionText = document.querySelector('#mission .lead');
                        if (missionText) {
                            missionText.textContent = settings.organization_mission;
                        }
                    }

                    // Update about section description
                    if (settings.organization_name) {
                        const aboutDescription = document.getElementById('about-description');
                        if (aboutDescription) {
                            const currentText = aboutDescription.textContent;
                            const updatedText = currentText.replace('Freedom Assembly Church International', settings.organization_name);
                            aboutDescription.textContent = updatedText;
                        }
                    }

                    if (settings.organization_vision) {
                        const aboutText = document.querySelector('#about .lead');
                        if (aboutText) {
                            aboutText.textContent = settings.organization_vision;
                        }
                    }

                    // Update contact information
                    if (settings.contact_info) {
                        const contact = settings.contact_info;

                        // Update email
                        if (contact.email) {
                            const emailElement = document.getElementById('contact-email');
                            if (emailElement) {
                                emailElement.textContent = contact.email;
                            }
                        }

                        // Update phone
                        if (contact.phone) {
                            const phoneElement = document.getElementById('contact-phone');
                            if (phoneElement) {
                                phoneElement.textContent = contact.phone;
                            }
                        }

                        // Update address
                        if (contact.address || contact.city || contact.state) {
                            const addressElement = document.getElementById('contact-address');
                            if (addressElement) {
                                let addressText = '';
                                if (contact.address) addressText += contact.address;
                                if (contact.city) addressText += (addressText ? '<br>' : '') + contact.city;
                                if (contact.state) addressText += (contact.city ? ', ' : (addressText ? '<br>' : '')) + contact.state;
                                if (contact.zip) addressText += ' ' + contact.zip;

                                if (addressText) {
                                    addressElement.innerHTML = addressText;
                                }
                            }
                        }
                    }

                    // Update social media links
                    if (settings.social_media) {
                        const social = settings.social_media;
                        const socialLinks = document.querySelectorAll('#contact .btn-outline-light');

                        if (social.facebook && socialLinks[0]) {
                            socialLinks[0].href = social.facebook;
                        }
                        if (social.instagram && socialLinks[1]) {
                            socialLinks[1].href = social.instagram;
                        }
                        if (social.twitter && socialLinks[2]) {
                            socialLinks[2].href = social.twitter;
                        }
                        if (social.youtube && socialLinks[3]) {
                            socialLinks[3].href = social.youtube;
                        }
                    }
                }
            } catch (error) {
                console.log('Could not load appearance settings:', error);
            }
        }

        // Load events from admin
        async function loadEvents() {
            try {
                const response = await fetch('church/api/get_public_events.php');
                const data = await response.json();

                const container = document.getElementById('events-container');

                if (data.success && data.events && data.events.length > 0) {
                    container.innerHTML = '';

                    data.events.slice(0, 3).forEach(event => {
                        const eventDate = new Date(event.event_date);
                        const eventCard = `
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="card h-100 border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 15px);">
                                    <div class="card-body p-4">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="badge" style="background-color: var(--bs-primary, #007bff); color: white; padding: 0.5rem 1rem; border-radius: var(--bs-border-radius, 10px);">
                                                ${eventDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                                            </div>
                                            <small style="color: var(--bs-body-color, #6c757d);">
                                                ${eventDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit' })}
                                            </small>
                                        </div>
                                        <h5 class="card-title mb-3" style="color: var(--bs-body-color, #212529);">${event.title}</h5>
                                        <p class="card-text mb-3" style="color: var(--bs-body-color, #6c757d);">
                                            <i class="bi bi-geo-alt me-2"></i>${event.location || 'Location TBD'}
                                        </p>
                                        <p class="card-text" style="color: var(--bs-body-color, #6c757d);">
                                            ${event.description ? event.description.substring(0, 100) + '...' : 'Join us for this exciting event!'}
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center mt-3">
                                            <small style="color: var(--bs-body-color, #6c757d);">
                                                <i class="bi bi-people me-1"></i>
                                                ${event.rsvp_count || 0} attending
                                            </small>
                                            <a href="church/user/events.php" class="btn btn-sm" style="background-color: var(--bs-primary, #007bff); color: white;">
                                                Learn More
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                        container.innerHTML += eventCard;
                    });
                } else {
                    container.innerHTML = `
                        <div class="col-12 text-center">
                            <div class="card border-0 shadow-sm" style="background: var(--bs-body-bg, white); border-radius: var(--bs-border-radius, 15px);">
                                <div class="card-body p-5">
                                    <i class="bi bi-calendar-x" style="font-size: 3rem; color: var(--bs-primary, #007bff); opacity: 0.5;"></i>
                                    <h5 class="mt-3" style="color: var(--bs-body-color, #212529);">No Upcoming Events</h5>
                                    <p style="color: var(--bs-body-color, #6c757d);">Check back soon for exciting events and activities!</p>
                                    <a href="church/user/events.php" class="btn" style="background-color: var(--bs-primary, #007bff); color: white;">
                                        View All Events
                                    </a>
                                </div>
                            </div>
                        </div>
                    `;
                }
            } catch (error) {
                console.log('Could not load events:', error);
                document.getElementById('events-container').innerHTML = `
                    <div class="col-12 text-center">
                        <p style="color: var(--bs-body-color, #6c757d);">Unable to load events at this time.</p>
                    </div>
                `;
            }
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Update navbar on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%)';
            }
        });

        // Scroll Animation Observer
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Navbar scroll effect
        function updateNavbarOnScroll() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.backgroundColor = 'rgba(0, 0, 0, 0.95)';
                navbar.style.backdropFilter = 'blur(20px)';
            } else {
                navbar.style.background = 'linear-gradient(135deg, var(--bs-primary, #007bff) 0%, var(--bs-secondary, #6c757d) 100%)';
                navbar.style.backdropFilter = 'blur(10px)';
            }
        }

        // Parallax effect for hero section
        function parallaxEffect() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.floating-element');

            parallaxElements.forEach((element, index) => {
                const speed = 0.5 + (index * 0.1);
                element.style.transform = `translateY(${scrolled * speed}px)`;
            });
        }

        // Add sparkle effect to buttons
        function addSparkleEffect() {
            const buttons = document.querySelectorAll('.btn-enhanced');

            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    // Create sparkle elements
                    for (let i = 0; i < 6; i++) {
                        const sparkle = document.createElement('div');
                        sparkle.className = 'sparkle';
                        sparkle.style.cssText = `
                            position: absolute;
                            width: 4px;
                            height: 4px;
                            background: white;
                            border-radius: 50%;
                            pointer-events: none;
                            animation: sparkle 0.6s ease-out forwards;
                            left: ${Math.random() * 100}%;
                            top: ${Math.random() * 100}%;
                        `;

                        this.appendChild(sparkle);

                        setTimeout(() => {
                            if (sparkle.parentNode) {
                                sparkle.parentNode.removeChild(sparkle);
                            }
                        }, 600);
                    }
                });
            });
        }

        // Add sparkle animation CSS
        const sparkleCSS = `
            @keyframes sparkle {
                0% {
                    transform: scale(0) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: scale(1) rotate(180deg);
                    opacity: 0;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = sparkleCSS;
        document.head.appendChild(styleSheet);

        // Load everything when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadAppearanceSettings();
            loadEvents();

            // Initialize scroll animations
            document.querySelectorAll('.section-animate').forEach(section => {
                observer.observe(section);
            });

            // Add event listeners
            window.addEventListener('scroll', updateNavbarOnScroll);
            window.addEventListener('scroll', parallaxEffect);

            // Initialize effects
            addSparkleEffect();

            // Add typing effect to hero title
            const heroTitle = document.querySelector('#hero h1');
            if (heroTitle) {
                const text = "Welcome to Freedom Assembly Church International";
                heroTitle.textContent = '';
                let i = 0;

                function typeWriter() {
                    if (i < text.length) {
                        heroTitle.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 50);
                    }
                }

                setTimeout(typeWriter, 1000);
            }
        });
    </script>
</body>
</html>
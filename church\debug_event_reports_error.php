<?php
// Debug Event Reports Error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Event Reports Error</h1>";

// Test 1: Check if config loads
echo "<h2>1. Config Loading Test</h2>";
try {
    require_once 'config.php';
    echo "<p style='color: green;'>✅ Config loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Config error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Check database connection
echo "<h2>2. Database Connection Test</h2>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ Database connection working</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 3: Check if functions exist
echo "<h2>3. Function Availability Test</h2>";
$functions = [
    'get_organization_name',
    'get_site_setting',
    'admin_url_for'
];

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ Function $func() exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Function $func() missing</p>";
    }
}

// Test 4: Check events table
echo "<h2>4. Events Table Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM events");
    $count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Events table accessible with $count events</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Events table error: " . $e->getMessage() . "</p>";
}

// Test 5: Check event_rsvps table
echo "<h2>5. Event RSVPs Table Test</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM event_rsvps");
    $count = $stmt->fetch()['count'];
    echo "<p style='color: green;'>✅ Event RSVPs table accessible with $count RSVPs</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Event RSVPs table error: " . $e->getMessage() . "</p>";
}

// Test 6: Test the exact query from event reports
echo "<h2>6. Event Reports Query Test</h2>";
try {
    $sql = "
        SELECT e.title, e.event_date, e.location, e.max_attendees, e.is_active,
               COUNT(er.id) as total_rsvps
        FROM events e
        LEFT JOIN event_rsvps er ON e.id = er.event_id
        WHERE e.event_date >= ? AND e.event_date <= ?
        GROUP BY e.id
        ORDER BY e.event_date DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([date('Y-m-01'), date('Y-m-t') . ' 23:59:59']);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ Event reports query works - found " . count($data) . " events</p>";
    
    if (!empty($data)) {
        echo "<p><strong>Sample event:</strong> " . htmlspecialchars($data[0]['title']) . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Query error: " . $e->getMessage() . "</p>";
}

// Test 7: Check if admin includes exist
echo "<h2>7. Admin Includes Test</h2>";
$includes = [
    'admin/includes/header.php',
    'admin/includes/footer.php',
    'admin/includes/sidebar.php'
];

foreach ($includes as $include) {
    if (file_exists(__DIR__ . '/' . $include)) {
        echo "<p style='color: green;'>✅ $include exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $include missing</p>";
    }
}

// Test 8: Simulate the event reports page loading
echo "<h2>8. Event Reports Page Simulation</h2>";
try {
    // Simulate session start
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Simulate admin login
    $_SESSION['admin_id'] = 1; // Temporary for testing
    
    echo "<p style='color: green;'>✅ Session simulation successful</p>";
    
    // Test the page variables
    $page_title = "Event Reports";
    $page_header = "Event Reports";
    $page_description = "Generate and download event attendance reports.";
    
    echo "<p style='color: green;'>✅ Page variables set successfully</p>";
    
    // Test events query for dropdown
    $events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
    $events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'>✅ Events dropdown query successful - " . count($events) . " events</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Page simulation error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>🔍 Diagnosis</h2>";
echo "<p>If all tests above pass, the issue might be:</p>";
echo "<ul>";
echo "<li>Session handling in the actual event_reports.php file</li>";
echo "<li>Include path issues</li>";
echo "<li>Function definition order problems</li>";
echo "<li>Output buffer conflicts</li>";
echo "</ul>";

echo "<p><strong>Next Step:</strong> Check the actual event_reports.php file for specific errors.</p>";
?>

<?php
session_start();
require_once '../config.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Set default response
$response = [
    'success' => false,
    'message' => 'An error occurred while sending the message.'
];

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    if (
        empty($_POST['member_id']) || 
        empty($_POST['template_id']) || 
        empty($_POST['subject']) || 
        empty($_POST['recipient_email']) || 
        empty($_POST['recipient_name'])
    ) {
        $_SESSION['error'] = 'All required fields must be filled out.';
        header('Location: birthday.php');
        exit;
    }

    try {
        // Get form data
        $member_id = $_POST['member_id'];
        $template_id = $_POST['template_id'];
        $manual_subject = $_POST['subject'];
        // Handle missing or invalid use_template_subject value
        $use_template_subject = isset($_POST['use_template_subject']) && $_POST['use_template_subject'] == '1' ? 1 : 0;
        $custom_message = $_POST['custom_message'] ?? '';
        $recipient_email = $_POST['recipient_email'];
        $recipient_name = $_POST['recipient_name'];
        
        // Log the received values for debugging
        error_log("Birthday email - Member ID: $member_id, Template ID: $template_id");
        error_log("Birthday email - Use Template Subject: $use_template_subject, Manual Subject: $manual_subject");

        // Get the email template
        $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE id = ?");
        $stmt->execute([$template_id]);
        $template = $stmt->fetch();

        if (!$template) {
            $_SESSION['error'] = 'Email template not found.';
            header('Location: birthday.php');
            exit;
        }

        // Debug information
        error_log("Template data: " . json_encode($template));
        
        // Check if content key exists
        if (!isset($template['content'])) {
            $_SESSION['error'] = 'Template content is missing.';
            error_log("Template content key is missing. Available keys: " . implode(", ", array_keys($template)));
            header('Location: birthday.php');
            exit;
        }

        // Replace placeholders in the template
        $body = $template['content']; // Using the correct key 'content'
        
        // Make sure $body is not null
        if (empty($body)) {
            $_SESSION['error'] = 'Template content is empty.';
            error_log("Template content is empty for template ID: " . $template_id);
            header('Location: birthday.php');
            exit;
        }
        
        // Get member details for image path
        $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
        $stmt->execute([$member_id]);
        $member = $stmt->fetch();

        if (!$member) {
            // Create a basic member data array if not found
            $member = [
                'full_name' => $recipient_name,
                'email' => $recipient_email,
                'phone_number' => '',
                'image_path' => '' // No image available
            ];
        }

        // Determine what subject to use
        if ($use_template_subject == 1) {
            // Use the template subject with placeholders replaced
            $subject = replaceTemplatePlaceholders($template['subject'], $member);
            error_log("Birthday email - Using template subject: " . $subject);
        } else {
            // Use the manual subject with placeholders replaced
            $subject = replaceTemplatePlaceholders($manual_subject, $member);
            error_log("Birthday email - Using manual subject: " . $subject);
        }
        
        // Set up image placeholders for the birthday member
        $siteUrl = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

        if (!empty($member['image_path'])) {
            $memberImageUrl = $siteUrl . '/' . ltrim($member['image_path'], '/');
        } else {
            $memberImageUrl = $siteUrl . '/assets/img/default-profile.jpg';
        }

        // Add image placeholders to member data
        $member['birthday_member_image'] = $memberImageUrl;
        $member['birthday_member_photo_url'] = $memberImageUrl;
        $member['member_image'] = $memberImageUrl;
        $member['member_image_url'] = $memberImageUrl;

        // Add birthday member name placeholders
        $member['birthday_member_name'] = $member['first_name'] ?? '';
        $member['birthday_member_first_name'] = $member['first_name'] ?? '';
        $member['birthday_member_full_name'] = $member['full_name'] ?? '';
        $member['birthday_member_email'] = $member['email'] ?? '';
        $member['birthday_member_phone'] = $member['phone_number'] ?? '';

        error_log("Birthday email - Setting image URL: " . $memberImageUrl);
        error_log("Birthday email - Member data keys: " . implode(', ', array_keys($member)));

        // Replace placeholders in the template body
        $body = replaceTemplatePlaceholders($template['content'], $member);
        
        // Add custom message if provided
        if (!empty($custom_message)) {
            $body = str_replace('{custom_message}', $custom_message, $body);
        } else {
            $body = str_replace('{custom_message}', '', $body);
        }

        // Send the email
        if (sendEmail($recipient_email, $recipient_name, $subject, $body, true, $member)) {
            // Log the email
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, sent_at) VALUES (?, ?, ?, 'success', NOW())");
            $stmt->execute([$member_id, $template_id, $subject]);
            
            // Generate a unique tracking ID
            $tracking_id = uniqid('track_', true);
            
            // Create tracking record
            $stmt = $pdo->prepare("INSERT INTO email_tracking (member_id, tracking_id, email_type, sent_at) VALUES (?, ?, 'birthday', NOW())");
            $stmt->execute([$member_id, $tracking_id]);
            
            $_SESSION['message'] = "Birthday message sent successfully to $recipient_name.";
        } else {
            // Log the failed email
            global $last_email_error;
            $error_message = $last_email_error ?? 'Unknown error';
            
            $stmt = $pdo->prepare("INSERT INTO email_logs (member_id, template_id, subject, status, error_message, sent_at) VALUES (?, ?, ?, 'failed', ?, NOW())");
            $stmt->execute([$member_id, $template_id, $subject, $error_message]);
            
            $_SESSION['error'] = "Failed to send email. Error: $error_message";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = 'Error: ' . $e->getMessage();
        error_log("Exception in send_birthday_message.php: " . $e->getMessage());
    }
}

// Redirect back to birthday page
header('Location: birthday.php');
exit; 
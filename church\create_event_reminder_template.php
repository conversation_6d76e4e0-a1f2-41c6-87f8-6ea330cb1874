<?php
require_once 'config.php';

echo "<h2>Create Event Reminder Email Template</h2>";

try {
    // Check if event reminder template already exists
    $stmt = $pdo->prepare("
        SELECT id, template_name FROM email_templates 
        WHERE template_name LIKE '%event%reminder%' 
        OR template_name LIKE '%reminder%event%'
        LIMIT 1
    ");
    $stmt->execute();
    $existingTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingTemplate) {
        echo "<p style='color: orange;'>⚠ Event reminder template already exists (ID: {$existingTemplate['id']}, Name: {$existingTemplate['template_name']})</p>";
        echo "<p>Updating existing template...</p>";
        
        // Update existing template
        $stmt = $pdo->prepare("
            UPDATE email_templates 
            SET subject = ?, content = ?, updated_at = NOW()
            WHERE id = ?
        ");
    } else {
        echo "<p style='color: blue;'>ℹ Creating new event reminder template...</p>";
        
        // Create new template
        $stmt = $pdo->prepare("
            INSERT INTO email_templates (template_name, subject, content, template_category, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
    }
    
    $templateName = "Event Reminder Template";
    $templateCategory = "general";
    $subject = "Reminder: {event_title} Tomorrow at {organization_name}";
    $content = '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 10px;">
    <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2c3e50; margin-bottom: 10px;">📅 Event Reminder</h1>
        <p style="color: #7f8c8d; font-size: 16px;">{organization_name}</p>
    </div>
    
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #2c3e50; margin-top: 0;">{event_title}</h2>
        <p style="margin: 10px 0; font-size: 16px;"><strong>📅 Date:</strong> {event_date}</p>
        <p style="margin: 10px 0; font-size: 16px;"><strong>🕐 Time:</strong> {event_time}</p>
        <p style="margin: 10px 0; font-size: 16px;"><strong>📍 Location:</strong> {event_location}</p>
        {event_description_section}
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <p style="font-size: 18px; color: #2c3e50;">We\'re looking forward to seeing you there, {first_name}!</p>
        <p style="color: #7f8c8d;">This is a friendly reminder that you RSVP\'d as attending this event.</p>
    </div>
    
    <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center; color: #7f8c8d; font-size: 14px;">
        <p>If you can no longer attend, please let us know as soon as possible.</p>
        <p>Best regards,<br>The {organization_name} Team</p>
    </div>
</div>';
    
    if ($existingTemplate) {
        $stmt->execute([$subject, $content, $existingTemplate['id']]);
        echo "<p style='color: green;'>✓ Event reminder template updated successfully!</p>";
    } else {
        $stmt->execute([$templateName, $subject, $content, $templateCategory]);
        $templateId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Event reminder template created successfully! (ID: $templateId)</p>";
    }
    
    echo "<h3>Template Details:</h3>";
    echo "<p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>";
    echo "<p><strong>Content Preview:</strong></p>";
    echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars($content);
    echo "</div>";
    
    echo "<h3>Available Placeholders:</h3>";
    echo "<ul>";
    echo "<li><code>{member_name}</code> - Full name of the member</li>";
    echo "<li><code>{first_name}</code> - First name of the member</li>";
    echo "<li><code>{last_name}</code> - Last name of the member</li>";
    echo "<li><code>{email}</code> - Email address of the member</li>";
    echo "<li><code>{event_title}</code> - Title of the event</li>";
    echo "<li><code>{event_date}</code> - Formatted date of the event</li>";
    echo "<li><code>{event_time}</code> - Formatted time of the event</li>";
    echo "<li><code>{event_location}</code> - Location of the event</li>";
    echo "<li><code>{event_description}</code> - Description of the event</li>";
    echo "<li><code>{event_description_section}</code> - Formatted description section (only shows if description exists)</li>";
    echo "<li><code>{organization_name}</code> - Name of the organization</li>";
    echo "<li><code>{organization_type}</code> - Type of organization (church, school, etc.)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error creating event reminder template: " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>

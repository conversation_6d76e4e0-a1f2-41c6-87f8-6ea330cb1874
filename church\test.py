#!/usr/bin/env python3
"""
Comprehensive Test Suite for Church Campaign Management System
Tests entire app flow from user registration to all pages and birthday email templates
"""

import requests
import json
import time
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import sys

class ChurchAppTester:
    def __init__(self, base_url="http://localhost/campaign/church"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        self.errors = []
        
    def log_test(self, test_name, status, message=""):
        """Log test results"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status_symbol = "✓" if status == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {message}")
        
        if status == "FAIL":
            self.errors.append(result)
    
    def test_page_accessibility(self, path, expected_status=200, test_name=None):
        """Test if a page is accessible"""
        if test_name is None:
            test_name = f"Page Access: {path}"
            
        try:
            url = urljoin(self.base_url, path)
            response = self.session.get(url, timeout=10)
            
            if response.status_code == expected_status:
                self.log_test(test_name, "PASS", f"Status: {response.status_code}")
                return response
            else:
                self.log_test(test_name, "FAIL", f"Expected {expected_status}, got {response.status_code}")
                return None
                
        except Exception as e:
            self.log_test(test_name, "FAIL", f"Error: {str(e)}")
            return None
    
    def test_user_registration(self):
        """Test user registration flow"""
        print("\n=== Testing User Registration ===")
        
        # Test registration page access
        response = self.test_page_accessibility("/register.php", test_name="Registration Page Access")
        if not response:
            return False
            
        # Parse form to get required fields
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form')
        
        if not form:
            self.log_test("Registration Form", "FAIL", "No form found on registration page")
            return False
            
        # Test form submission with sample data
        test_data = {
            'full_name': 'Test User',
            'email': f'test_{int(time.time())}@example.com',
            'phone_number': '+1234567890',
            'birth_date': '1990-01-01',
            'home_address': '123 Test St',
            'occupation': 'Tester'
        }
        
        try:
            response = self.session.post(
                urljoin(self.base_url, "/process_registration.php"),
                data=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                if "success" in response.text.lower() or "welcome" in response.text.lower():
                    self.log_test("User Registration", "PASS", "Registration successful")
                    return True
                else:
                    self.log_test("User Registration", "FAIL", "Registration may have failed")
                    return False
            else:
                self.log_test("User Registration", "FAIL", f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("User Registration", "FAIL", f"Error: {str(e)}")
            return False
    
    def test_admin_pages(self):
        """Test admin panel accessibility"""
        print("\n=== Testing Admin Pages ===")
        
        admin_pages = [
            "/admin/login.php",
            "/admin/dashboard.php",
            "/admin/members.php",
            "/admin/email_templates.php",
            "/admin/birthday.php",
            "/admin/bulk_email.php",
            "/admin/settings.php"
        ]
        
        for page in admin_pages:
            self.test_page_accessibility(page, expected_status=200)
    
    def test_user_pages(self):
        """Test user-facing pages"""
        print("\n=== Testing User Pages ===")
        
        user_pages = [
            "/index.php",
            "/events.php",
            "/user/login.php",
            "/user/register.php",
            "/user/dashboard.php",
            "/user/profile.php",
            "/user/events.php"
        ]
        
        for page in user_pages:
            self.test_page_accessibility(page, expected_status=200)
    
    def test_birthday_templates(self):
        """Test birthday email templates"""
        print("\n=== Testing Birthday Templates ===")
        
        # Test template listing page
        response = self.test_page_accessibility("/admin/email_templates.php", test_name="Email Templates Page")
        
        # Test shortcode validation
        response = self.test_page_accessibility("/test_shortcodes.php", test_name="Shortcode Validation")
        if response:
            content = response.text
            if "All shortcodes were successfully replaced" in content:
                self.log_test("Shortcode Validation", "PASS", "All shortcodes working")
            elif "Unreplaced Shortcodes Found" in content:
                self.log_test("Shortcode Validation", "FAIL", "Some shortcodes not working")
            else:
                self.log_test("Shortcode Validation", "WARN", "Could not determine shortcode status")
        
        # Test birthday template checker
        response = self.test_page_accessibility("/check_birthday_templates.php", test_name="Birthday Template Check")
        
        # Test birthday email functionality
        response = self.test_page_accessibility("/admin/test_birthday_email.php", test_name="Birthday Email Test Page")
    
    def test_email_functionality(self):
        """Test email system"""
        print("\n=== Testing Email Functionality ===")
        
        # Test email settings
        response = self.test_page_accessibility("/admin/email_settings.php", test_name="Email Settings Page")
        
        # Test SMTP configuration
        response = self.test_page_accessibility("/check_smtp.php", test_name="SMTP Check")
        
        # Test email template functionality
        response = self.test_page_accessibility("/test_email_functionality.php", test_name="Email Functionality Test")
    
    def test_database_connectivity(self):
        """Test database connections"""
        print("\n=== Testing Database Connectivity ===")
        
        # Test database connection
        response = self.test_page_accessibility("/check_database.php", test_name="Database Connection")
        
        # Test member data
        response = self.test_page_accessibility("/check_members.php", test_name="Members Table Check")
        
        # Test email templates table
        response = self.test_page_accessibility("/check_email_templates.php", test_name="Email Templates Table Check")
    
    def test_image_handling(self):
        """Test image upload and handling"""
        print("\n=== Testing Image Handling ===")
        
        # Test image paths
        response = self.test_page_accessibility("/admin/test_image_paths.php", test_name="Image Paths Test")
        
        # Test member images
        response = self.test_page_accessibility("/admin/debug_member_image.php", test_name="Member Image Debug")
    
    def test_birthday_email_issues(self):
        """Test specific birthday email issues mentioned by user"""
        print("\n=== Testing Birthday Email Issues ===")

        # Test for receipt image attachment issue
        try:
            response = self.session.get(urljoin(self.base_url, "/test_shortcodes.php"))
            if response.status_code == 200:
                content = response.text

                # Check if receipt images are mentioned
                if "receipt" in content.lower():
                    self.log_test("Receipt Image Check", "WARN", "Receipt references found in templates")
                else:
                    self.log_test("Receipt Image Check", "PASS", "No receipt image references")

                # Check for celebration images
                if "celebration" in content.lower() or "birthday" in content.lower():
                    self.log_test("Celebration Image Check", "PASS", "Birthday/celebration content found")
                else:
                    self.log_test("Celebration Image Check", "WARN", "Limited celebration content")

        except Exception as e:
            self.log_test("Birthday Email Issues", "FAIL", f"Error: {str(e)}")

        # Test specific shortcodes mentioned in user's email
        shortcodes_to_test = [
            "{birthday_member_photo_url}",
            "{member_image}",
            "{birthday_member_name}",
            "{birthday_member_full_name}",
            "{upcoming_birthday_formatted}",
            "{birthday_member_age}",
            "{days_text}",
            "{church_name}"
        ]

        try:
            response = self.session.get(urljoin(self.base_url, "/check_birthday_templates.php"))
            if response.status_code == 200:
                content = response.text

                for shortcode in shortcodes_to_test:
                    if shortcode in content:
                        self.log_test(f"Shortcode {shortcode}", "PASS", "Found in templates")
                    else:
                        self.log_test(f"Shortcode {shortcode}", "WARN", "Not found in template check")

        except Exception as e:
            self.log_test("Shortcode Template Check", "FAIL", f"Error: {str(e)}")

        # Test image attachment logic
        try:
            response = self.session.get(urljoin(self.base_url, "/admin/debug_images.php"))
            if response.status_code == 200:
                self.log_test("Image Debug Page", "PASS", "Image debug accessible")
            else:
                self.log_test("Image Debug Page", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Image Debug Page", "FAIL", f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("Starting Comprehensive Church App Test Suite")
        print("=" * 50)
        
        start_time = time.time()
        
        # Run all test categories
        self.test_database_connectivity()
        self.test_user_registration()
        self.test_user_pages()
        self.test_admin_pages()
        self.test_birthday_templates()
        self.test_email_functionality()
        self.test_image_handling()
        self.test_birthday_email_issues()
        
        end_time = time.time()
        
        # Generate summary report
        self.generate_report(end_time - start_time)
    
    def generate_report(self, duration):
        """Generate test report"""
        print("\n" + "=" * 50)
        print("TEST SUMMARY REPORT")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warned_tests = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Warnings: {warned_tests}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print(f"\n{len(self.errors)} FAILED TESTS:")
            for error in self.errors:
                print(f"  ✗ {error['test']}: {error['message']}")
        
        # Save detailed report to file
        try:
            with open('test_report.json', 'w') as f:
                json.dump({
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'warnings': warned_tests,
                        'duration': duration,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            print(f"\nDetailed report saved to: test_report.json")
        except Exception as e:
            print(f"Could not save report: {e}")

if __name__ == "__main__":
    # Allow custom base URL
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost/campaign/church"
    
    tester = ChurchAppTester(base_url)
    tester.run_all_tests()

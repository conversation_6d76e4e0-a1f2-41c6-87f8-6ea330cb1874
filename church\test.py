#!/usr/bin/env python3
"""
Comprehensive Test Suite for Church Campaign Management System
Tests entire application including all modules, user flows, admin functions, and integrations
"""

import requests
import json
import time
import re
import os
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import sys
from datetime import datetime, timedelta

class ChurchAppTester:
    def __init__(self, base_url="http://localhost/campaign/church"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        self.errors = []
        
    def log_test(self, test_name, status, message=""):
        """Log test results"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        status_symbol = "✓" if status == "PASS" else "✗"
        print(f"{status_symbol} {test_name}: {message}")
        
        if status == "FAIL":
            self.errors.append(result)
    
    def test_page_accessibility(self, path, expected_status=200, test_name=None):
        """Test if a page is accessible"""
        if test_name is None:
            test_name = f"Page Access: {path}"
            
        try:
            url = urljoin(self.base_url, path)
            response = self.session.get(url, timeout=10)
            
            if response.status_code == expected_status:
                self.log_test(test_name, "PASS", f"Status: {response.status_code}")
                return response
            else:
                self.log_test(test_name, "FAIL", f"Expected {expected_status}, got {response.status_code}")
                return None
                
        except Exception as e:
            self.log_test(test_name, "FAIL", f"Error: {str(e)}")
            return None
    
    def test_user_registration(self):
        """Test user registration flow"""
        print("\n=== Testing User Registration ===")
        
        # Test registration page access
        response = self.test_page_accessibility("/register.php", test_name="Registration Page Access")
        if not response:
            return False
            
        # Parse form to get required fields
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form')
        
        if not form:
            self.log_test("Registration Form", "FAIL", "No form found on registration page")
            return False
            
        # Test form submission with sample data
        test_data = {
            'full_name': 'Test User',
            'email': f'test_{int(time.time())}@example.com',
            'phone_number': '+1234567890',
            'birth_date': '1990-01-01',
            'home_address': '123 Test St',
            'occupation': 'Tester'
        }
        
        try:
            response = self.session.post(
                urljoin(self.base_url, "/process_registration.php"),
                data=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                if "success" in response.text.lower() or "welcome" in response.text.lower():
                    self.log_test("User Registration", "PASS", "Registration successful")
                    return True
                else:
                    self.log_test("User Registration", "FAIL", "Registration may have failed")
                    return False
            else:
                self.log_test("User Registration", "FAIL", f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("User Registration", "FAIL", f"Error: {str(e)}")
            return False
    
    def test_admin_pages(self):
        """Test admin panel accessibility and functionality"""
        print("\n=== Testing Admin Pages ===")

        admin_pages = [
            "/admin/login.php",
            "/admin/dashboard.php",
            "/admin/members.php",
            "/admin/add_member.php",
            "/admin/email_templates.php",
            "/admin/automated_email_templates.php",
            "/admin/birthday.php",
            "/admin/bulk_email.php",
            "/admin/email_scheduler.php",
            "/admin/settings.php",
            "/admin/appearance_settings.php",
            "/admin/site_settings.php",
            "/admin/email_settings.php",
            "/admin/security_settings.php",
            "/admin/events.php",
            "/admin/donations.php",
            "/admin/payment_integration.php",
            "/admin/contacts.php",
            "/admin/contact_groups.php",
            "/admin/whatsapp_templates.php",
            "/admin/whatsapp_messages.php",
            "/admin/email_analytics.php",
            "/admin/about_shortcodes.php"
        ]

        for page in admin_pages:
            self.test_page_accessibility(page, expected_status=200)
    
    def test_user_pages(self):
        """Test user-facing pages and functionality"""
        print("\n=== Testing User Pages ===")

        user_pages = [
            "/index.php",
            "/events.php",
            "/event_detail.php",
            "/donate.php",
            "/user/login.php",
            "/user/register.php",
            "/user/dashboard.php",
            "/user/profile.php",
            "/user/events.php",
            "/user/event_detail.php",
            "/user/settings.php",
            "/user/birthday_templates.php",
            "/user/send_birthday_message.php",
            "/user/change_password.php",
            "/user/forgot_password.php"
        ]

        for page in user_pages:
            self.test_page_accessibility(page, expected_status=200)
    
    def test_birthday_templates(self):
        """Test birthday email templates"""
        print("\n=== Testing Birthday Templates ===")
        
        # Test template listing page
        response = self.test_page_accessibility("/admin/email_templates.php", test_name="Email Templates Page")
        
        # Test shortcode validation
        response = self.test_page_accessibility("/test_shortcodes.php", test_name="Shortcode Validation")
        if response:
            content = response.text
            if "All shortcodes were successfully replaced" in content:
                self.log_test("Shortcode Validation", "PASS", "All shortcodes working")
            elif "Unreplaced Shortcodes Found" in content:
                self.log_test("Shortcode Validation", "FAIL", "Some shortcodes not working")
            else:
                self.log_test("Shortcode Validation", "WARN", "Could not determine shortcode status")
        
        # Test birthday template checker
        response = self.test_page_accessibility("/check_birthday_templates.php", test_name="Birthday Template Check")
        
        # Test birthday email functionality
        response = self.test_page_accessibility("/admin/test_birthday_email.php", test_name="Birthday Email Test Page")
    
    def test_email_functionality(self):
        """Test email system"""
        print("\n=== Testing Email Functionality ===")
        
        # Test email settings
        response = self.test_page_accessibility("/admin/email_settings.php", test_name="Email Settings Page")
        
        # Test SMTP configuration
        response = self.test_page_accessibility("/check_smtp.php", test_name="SMTP Check")
        
        # Test email template functionality
        response = self.test_page_accessibility("/test_email_functionality.php", test_name="Email Functionality Test")
    
    def test_database_connectivity(self):
        """Test database connections"""
        print("\n=== Testing Database Connectivity ===")
        
        # Test database connection
        response = self.test_page_accessibility("/check_database.php", test_name="Database Connection")
        
        # Test member data
        response = self.test_page_accessibility("/check_members.php", test_name="Members Table Check")
        
        # Test email templates table
        response = self.test_page_accessibility("/check_email_templates.php", test_name="Email Templates Table Check")
    
    def test_image_handling(self):
        """Test image upload and handling"""
        print("\n=== Testing Image Handling ===")
        
        # Test image paths
        response = self.test_page_accessibility("/admin/test_image_paths.php", test_name="Image Paths Test")
        
        # Test member images
        response = self.test_page_accessibility("/admin/debug_member_image.php", test_name="Member Image Debug")
    
    def test_birthday_email_issues(self):
        """Test specific birthday email issues mentioned by user"""
        print("\n=== Testing Birthday Email Issues ===")

        # Test for receipt image attachment issue
        try:
            response = self.session.get(urljoin(self.base_url, "/test_shortcodes.php"))
            if response.status_code == 200:
                content = response.text

                # Check if receipt images are mentioned
                if "receipt" in content.lower():
                    self.log_test("Receipt Image Check", "WARN", "Receipt references found in templates")
                else:
                    self.log_test("Receipt Image Check", "PASS", "No receipt image references")

                # Check for celebration images
                if "celebration" in content.lower() or "birthday" in content.lower():
                    self.log_test("Celebration Image Check", "PASS", "Birthday/celebration content found")
                else:
                    self.log_test("Celebration Image Check", "WARN", "Limited celebration content")

        except Exception as e:
            self.log_test("Birthday Email Issues", "FAIL", f"Error: {str(e)}")

        # Test specific shortcodes mentioned in user's email
        shortcodes_to_test = [
            "{birthday_member_photo_url}",
            "{member_image}",
            "{birthday_member_name}",
            "{birthday_member_full_name}",
            "{upcoming_birthday_formatted}",
            "{birthday_member_age}",
            "{days_text}",
            "{church_name}"
        ]

        try:
            response = self.session.get(urljoin(self.base_url, "/check_birthday_templates.php"))
            if response.status_code == 200:
                content = response.text

                for shortcode in shortcodes_to_test:
                    if shortcode in content:
                        self.log_test(f"Shortcode {shortcode}", "PASS", "Found in templates")
                    else:
                        self.log_test(f"Shortcode {shortcode}", "WARN", "Not found in template check")

        except Exception as e:
            self.log_test("Shortcode Template Check", "FAIL", f"Error: {str(e)}")

        # Test image attachment logic
        try:
            response = self.session.get(urljoin(self.base_url, "/admin/debug_images.php"))
            if response.status_code == 200:
                self.log_test("Image Debug Page", "PASS", "Image debug accessible")
            else:
                self.log_test("Image Debug Page", "FAIL", f"Status: {response.status_code}")
        except Exception as e:
            self.log_test("Image Debug Page", "FAIL", f"Error: {str(e)}")

    def test_user_authentication(self):
        """Test user authentication system"""
        print("\n=== Testing User Authentication ===")

        # Test login page
        response = self.test_page_accessibility("/user/login.php", test_name="User Login Page")

        # Test registration page
        response = self.test_page_accessibility("/user/register.php", test_name="User Registration Page")

        # Test password reset
        response = self.test_page_accessibility("/user/forgot_password.php", test_name="Password Reset Page")

        # Test admin login
        response = self.test_page_accessibility("/admin/login.php", test_name="Admin Login Page")

    def test_event_management(self):
        """Test event management system"""
        print("\n=== Testing Event Management ===")

        # Test event pages
        event_pages = [
            "/admin/events.php",
            "/events.php",
            "/event_detail.php",
            "/user/events.php",
            "/user/event_detail.php"
        ]

        for page in event_pages:
            self.test_page_accessibility(page)

        # Test RSVP functionality
        response = self.test_page_accessibility("/rsvp_handler.php", test_name="RSVP Handler")
        response = self.test_page_accessibility("/user/rsvp_handler.php", test_name="User RSVP Handler")

    def test_member_management(self):
        """Test member management system"""
        print("\n=== Testing Member Management ===")

        # Test member pages
        member_pages = [
            "/admin/members.php",
            "/admin/add_member.php",
            "/admin/edit_member.php",
            "/admin/view_member.php",
            "/user/profile.php"
        ]

        for page in member_pages:
            self.test_page_accessibility(page)

    def test_email_system(self):
        """Test comprehensive email system"""
        print("\n=== Testing Email System ===")

        # Test email pages
        email_pages = [
            "/admin/email_templates.php",
            "/admin/automated_email_templates.php",
            "/admin/bulk_email.php",
            "/admin/email_scheduler.php",
            "/admin/email_settings.php",
            "/admin/email_analytics.php",
            "/admin/test_birthday_email.php"
        ]

        for page in email_pages:
            self.test_page_accessibility(page)

        # Test template analysis
        response = self.test_page_accessibility("/comprehensive_template_analysis.php", test_name="Template Analysis")

    def test_payment_system(self):
        """Test payment and donation system"""
        print("\n=== Testing Payment System ===")

        # Test payment pages
        payment_pages = [
            "/donate.php",
            "/process_donation.php",
            "/admin/donations.php",
            "/admin/payment_integration.php",
            "/donation_success.php",
            "/donation_error.php",
            "/complete_paypal_payment.php"
        ]

        for page in payment_pages:
            self.test_page_accessibility(page)

    def test_notification_system(self):
        """Test notification and messaging system"""
        print("\n=== Testing Notification System ===")

        # Test notification pages
        notification_pages = [
            "/admin/whatsapp_templates.php",
            "/admin/whatsapp_messages.php",
            "/admin/send_birthday_notification.php",
            "/user/send_birthday_message.php"
        ]

        for page in notification_pages:
            self.test_page_accessibility(page)

    def test_file_upload_system(self):
        """Test file upload and image handling"""
        print("\n=== Testing File Upload System ===")

        # Test upload directories exist
        upload_dirs = ["/uploads", "/uploads/profiles", "/uploads/events"]
        for directory in upload_dirs:
            try:
                response = self.session.get(urljoin(self.base_url, directory))
                if response.status_code in [200, 403]:  # 403 is OK for directory listing disabled
                    self.log_test(f"Upload Directory {directory}", "PASS", "Directory accessible")
                else:
                    self.log_test(f"Upload Directory {directory}", "FAIL", f"Status: {response.status_code}")
            except Exception as e:
                self.log_test(f"Upload Directory {directory}", "FAIL", f"Error: {str(e)}")

        # Test image handling pages
        image_pages = [
            "/admin/debug_images.php",
            "/admin/test_image_paths.php",
            "/admin/debug_member_image.php"
        ]

        for page in image_pages:
            self.test_page_accessibility(page)

    def test_security_features(self):
        """Test security features and settings"""
        print("\n=== Testing Security Features ===")

        # Test security pages
        security_pages = [
            "/admin/security_settings.php",
            "/admin/security_audit.php",
            "/test_2fa.php"
        ]

        for page in security_pages:
            self.test_page_accessibility(page)

    def test_api_endpoints(self):
        """Test API endpoints and AJAX handlers"""
        print("\n=== Testing API Endpoints ===")

        # Test API endpoints
        api_endpoints = [
            "/api/birthdays.php",
            "/api/check_database.php",
            "/api/check_members_table.php",
            "/ajax/get_recipient_details.php",
            "/ajax/send_single_email.php",
            "/user/ajax/get_event_details.php"
        ]

        for endpoint in api_endpoints:
            self.test_page_accessibility(endpoint)

    def test_production_readiness(self):
        """Test production deployment readiness"""
        print("\n=== Testing Production Readiness ===")

        # Test configuration files
        config_pages = [
            "/check_database.php",
            "/check_smtp.php",
            "/phpinfo.php"
        ]

        for page in config_pages:
            response = self.test_page_accessibility(page)
            if response and page == "/phpinfo.php":
                # Check for sensitive information exposure
                if "phpinfo()" in response.text.lower():
                    self.log_test("PHPInfo Security", "WARN", "PHPInfo accessible - should be disabled in production")

        # Test for hardcoded localhost references
        test_pages = ["/", "/admin/", "/user/"]
        for page in test_pages:
            try:
                response = self.session.get(urljoin(self.base_url, page))
                if response.status_code == 200:
                    content = response.text.lower()
                    if "localhost" in content or "127.0.0.1" in content:
                        self.log_test(f"Hardcoded Localhost Check {page}", "WARN", "Localhost references found")
                    else:
                        self.log_test(f"Hardcoded Localhost Check {page}", "PASS", "No localhost references")
            except Exception as e:
                self.log_test(f"Hardcoded Localhost Check {page}", "FAIL", f"Error: {str(e)}")

    def test_appearance_settings(self):
        """Test appearance settings and UI layout"""
        print("\n=== Testing Appearance Settings ===")

        # Test appearance settings page
        response = self.test_page_accessibility("/admin/appearance_settings.php", test_name="Appearance Settings Page")

        if response:
            # Check for layout issues
            soup = BeautifulSoup(response.text, 'html.parser')

            # Check for sidebar overlap issues
            sidebar = soup.find(class_='sidebar')
            main_content = soup.find(class_='main-content')

            if sidebar and main_content:
                self.log_test("Layout Structure", "PASS", "Sidebar and main content found")
            else:
                self.log_test("Layout Structure", "WARN", "Layout structure may have issues")

            # Check for CSS includes
            css_links = soup.find_all('link', rel='stylesheet')
            if css_links:
                self.log_test("CSS Includes", "PASS", f"Found {len(css_links)} CSS files")
            else:
                self.log_test("CSS Includes", "WARN", "No CSS files found")

    def test_system_integration(self):
        """Test system integration and cross-module functionality"""
        print("\n=== Testing System Integration ===")

        # Test cron jobs and automated tasks
        cron_pages = [
            "/cron/birthday_reminders.php",
            "/cron/event_reminders.php",
            "/cron/process_birthday_reminders.php",
            "/cron/process_email_queue.php"
        ]

        for page in cron_pages:
            self.test_page_accessibility(page)

        # Test comprehensive functionality
        comprehensive_tests = [
            "/comprehensive_test.php",
            "/test_system.php",
            "/final_test.php"
        ]

        for test in comprehensive_tests:
            self.test_page_accessibility(test)
    
    def run_all_tests(self):
        """Run all tests"""
        print("Starting Comprehensive Church App Test Suite")
        print("=" * 50)
        
        start_time = time.time()
        
        # Run all test categories
        self.test_database_connectivity()
        self.test_user_registration()
        self.test_user_authentication()
        self.test_user_pages()
        self.test_admin_pages()
        self.test_event_management()
        self.test_member_management()
        self.test_email_system()
        self.test_birthday_templates()
        self.test_payment_system()
        self.test_notification_system()
        self.test_file_upload_system()
        self.test_security_features()
        self.test_api_endpoints()
        self.test_production_readiness()
        self.test_appearance_settings()
        self.test_system_integration()
        
        end_time = time.time()
        
        # Generate summary report
        self.generate_report(end_time - start_time)
    
    def generate_report(self, duration):
        """Generate test report"""
        print("\n" + "=" * 50)
        print("TEST SUMMARY REPORT")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in self.test_results if r['status'] == 'FAIL'])
        warned_tests = len([r for r in self.test_results if r['status'] == 'WARN'])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Warnings: {warned_tests}")
        print(f"Duration: {duration:.2f} seconds")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.errors:
            print(f"\n{len(self.errors)} FAILED TESTS:")
            for error in self.errors:
                print(f"  ✗ {error['test']}: {error['message']}")
        
        # Save detailed report to file
        try:
            with open('test_report.json', 'w') as f:
                json.dump({
                    'summary': {
                        'total': total_tests,
                        'passed': passed_tests,
                        'failed': failed_tests,
                        'warnings': warned_tests,
                        'duration': duration,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            print(f"\nDetailed report saved to: test_report.json")
        except Exception as e:
            print(f"Could not save report: {e}")

if __name__ == "__main__":
    # Allow custom base URL
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost/campaign/church"
    
    tester = ChurchAppTester(base_url)
    tester.run_all_tests()

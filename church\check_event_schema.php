<?php
require_once 'config.php';

echo "<h2>Event Database Schema Check</h2>";

// Check if events table exists and its structure
echo "<h3>1. Events Table Structure</h3>";
try {
    $stmt = $pdo->query("DESCRIBE events");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<p style='color: green;'>✓ Events table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ Events table not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking events table: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check if event_rsvps table exists and its structure
echo "<h3>2. Event RSVPs Table Structure</h3>";
try {
    $stmt = $pdo->query("DESCRIBE event_rsvps");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<p style='color: green;'>✓ Event RSVPs table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ Event RSVPs table not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking event_rsvps table: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check for sample data
echo "<h3>3. Sample Data Check</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as event_count FROM events");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Total events in database: " . $result['event_count'] . "</p>";
    
    if ($result['event_count'] > 0) {
        $stmt = $pdo->query("SELECT id, title, event_date, status FROM events ORDER BY event_date DESC LIMIT 5");
        $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p>Recent events:</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Status</th></tr>";
        foreach ($events as $event) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($event['id']) . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td>" . htmlspecialchars($event['event_date']) . "</td>";
            echo "<td>" . htmlspecialchars($event['status'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as rsvp_count FROM event_rsvps");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Total RSVPs in database: " . $result['rsvp_count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking sample data: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check for email_logs table (used by reminder system)
echo "<h3>4. Email Logs Table Check</h3>";
try {
    $stmt = $pdo->query("DESCRIBE email_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<p style='color: green;'>✓ Email logs table exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Email logs table not found - will need to create it</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠ Email logs table not found: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check existing event reminder system
echo "<h3>5. Event Reminder System Check</h3>";
$reminderFile = __DIR__ . '/admin/event_reminder_system.php';
if (file_exists($reminderFile)) {
    echo "<p style='color: green;'>✓ Event reminder system file exists</p>";
} else {
    echo "<p style='color: red;'>❌ Event reminder system file not found</p>";
}

// Check for upcoming events (next 48 hours)
echo "<h3>6. Upcoming Events Check</h3>";
try {
    $tomorrow = date('Y-m-d H:i:s', strtotime('+24 hours'));
    $dayAfterTomorrow = date('Y-m-d H:i:s', strtotime('+48 hours'));
    
    $stmt = $pdo->prepare("
        SELECT id, title, event_date, status 
        FROM events 
        WHERE event_date BETWEEN ? AND ?
        ORDER BY event_date ASC
    ");
    $stmt->execute([$tomorrow, $dayAfterTomorrow]);
    $upcomingEvents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Events in next 24-48 hours: " . count($upcomingEvents) . "</p>";
    
    if (count($upcomingEvents) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Title</th><th>Date</th><th>Status</th></tr>";
        foreach ($upcomingEvents as $event) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($event['id']) . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td>" . htmlspecialchars($event['event_date']) . "</td>";
            echo "<td>" . htmlspecialchars($event['status'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking upcoming events: " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>

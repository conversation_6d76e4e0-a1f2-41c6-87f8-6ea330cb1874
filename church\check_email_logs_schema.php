<?php
require_once 'config.php';

echo "<h2>Email Logs Schema Check</h2>";

try {
    $stmt = $pdo->query("DESCRIBE email_logs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<p style='color: green;'>✓ Email logs table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check sample data
        echo "<h3>Sample Data</h3>";
        $stmt = $pdo->query("SELECT * FROM email_logs ORDER BY id DESC LIMIT 5");
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($logs) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            $firstRow = true;
            foreach ($logs as $log) {
                if ($firstRow) {
                    echo "<tr>";
                    foreach (array_keys($log) as $key) {
                        echo "<th>" . htmlspecialchars($key) . "</th>";
                    }
                    echo "</tr>";
                    $firstRow = false;
                }
                echo "<tr>";
                foreach ($log as $value) {
                    echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No email logs found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Email logs table not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>

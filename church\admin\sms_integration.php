<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Initialize variables
$message = '';
$error = '';
$sms_******** = [];

// Function to get SMS ********
function getSMS********($conn) {
    try {
        $stmt = $conn->prepare("SELECT * FROM sms_******** WHERE 1");
        $stmt->execute();
        $******** = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($******** as $setting) {
            $result[$setting['setting_key']] = $setting['setting_value'];
        }
        
        return $result;
    } catch (PDOException $e) {
        error_log("Error fetching SMS ********: " . $e->getMessage());
        return [];
    }
}

// Function to update SMS ********
function updateSMSSetting($conn, $key, $value) {
    try {
        // Check if setting exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM sms_******** WHERE setting_key = ?");
        $stmt->execute([$key]);
        $exists = (int)$stmt->fetchColumn();
        
        if ($exists) {
            // Update existing setting
            $stmt = $conn->prepare("UPDATE sms_******** SET setting_value = ? WHERE setting_key = ?");
            $stmt->execute([$value, $key]);
        } else {
            // Insert new setting
            $stmt = $conn->prepare("INSERT INTO sms_******** (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute([$key, $value]);
        }
        
        return true;
    } catch (PDOException $e) {
        error_log("Error updating SMS setting: " . $e->getMessage());
        return false;
    }
}

// Check if sms_******** table exists, if not create it
try {
    $stmt = $conn->prepare("SHOW TABLES LIKE 'sms_********'");
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        $sql = "CREATE TABLE sms_******** (
            id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(255) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        
        // Insert default ********
        $default******** = [
            'twilio_enabled' => '0',
            'twilio_account_sid' => '',
            'twilio_auth_token' => '',
            'twilio_phone_number' => '',
            'nexmo_enabled' => '0',
            'nexmo_api_key' => '',
            'nexmo_api_secret' => '',
            'nexmo_from' => '',
            'whatsapp_enabled' => '0',
            'whatsapp_api_key' => '',
            'whatsapp_api_secret' => '',
            'whatsapp_phone_number' => '',
            'sms_notifications_enabled' => '1',
            'birthday_sms_enabled' => '1',
            'event_sms_enabled' => '1',
            'birthday_sms_template' => 'Happy Birthday {name}! May God bless you abundantly on your special day. - Freedom Assembly Church',
            'event_sms_template' => 'Reminder: {event_name} on {event_date} at {event_time}. {event_location}. - Freedom Assembly Church',
            'default_country_code' => '+1'
        ];
        
        foreach ($default******** as $key => $value) {
            updateSMSSetting($conn, $key, $value);
        }
    }
} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    error_log("Error creating sms_******** table: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Twilio ********
        if (isset($_POST['twilio_********'])) {
            $twilio_enabled = isset($_POST['twilio_enabled']) ? '1' : '0';
            $twilio_account_sid = trim($_POST['twilio_account_sid']);
            $twilio_auth_token = trim($_POST['twilio_auth_token']);
            $twilio_phone_number = trim($_POST['twilio_phone_number']);
            
            updateSMSSetting($conn, 'twilio_enabled', $twilio_enabled);
            updateSMSSetting($conn, 'twilio_account_sid', $twilio_account_sid);
            updateSMSSetting($conn, 'twilio_auth_token', $twilio_auth_token);
            updateSMSSetting($conn, 'twilio_phone_number', $twilio_phone_number);
            
            $message = "Twilio ******** updated successfully.";
        }
        
        // Nexmo ********
        if (isset($_POST['nexmo_********'])) {
            $nexmo_enabled = isset($_POST['nexmo_enabled']) ? '1' : '0';
            $nexmo_api_key = trim($_POST['nexmo_api_key']);
            $nexmo_api_secret = trim($_POST['nexmo_api_secret']);
            $nexmo_from = trim($_POST['nexmo_from']);
            
            updateSMSSetting($conn, 'nexmo_enabled', $nexmo_enabled);
            updateSMSSetting($conn, 'nexmo_api_key', $nexmo_api_key);
            updateSMSSetting($conn, 'nexmo_api_secret', $nexmo_api_secret);
            updateSMSSetting($conn, 'nexmo_from', $nexmo_from);
            
            $message = "Nexmo ******** updated successfully.";
        }
        
        // WhatsApp ********
        if (isset($_POST['whatsapp_********'])) {
            $whatsapp_enabled = isset($_POST['whatsapp_enabled']) ? '1' : '0';
            $whatsapp_api_key = trim($_POST['whatsapp_api_key']);
            $whatsapp_api_secret = trim($_POST['whatsapp_api_secret']);
            $whatsapp_phone_number = trim($_POST['whatsapp_phone_number']);
            
            updateSMSSetting($conn, 'whatsapp_enabled', $whatsapp_enabled);
            updateSMSSetting($conn, 'whatsapp_api_key', $whatsapp_api_key);
            updateSMSSetting($conn, 'whatsapp_api_secret', $whatsapp_api_secret);
            updateSMSSetting($conn, 'whatsapp_phone_number', $whatsapp_phone_number);
            
            $message = "WhatsApp ******** updated successfully.";
        }
        
        // General SMS ********
        if (isset($_POST['general_sms_********'])) {
            $sms_notifications_enabled = isset($_POST['sms_notifications_enabled']) ? '1' : '0';
            $birthday_sms_enabled = isset($_POST['birthday_sms_enabled']) ? '1' : '0';
            $event_sms_enabled = isset($_POST['event_sms_enabled']) ? '1' : '0';
            $birthday_sms_template = trim($_POST['birthday_sms_template']);
            $event_sms_template = trim($_POST['event_sms_template']);
            $default_country_code = trim($_POST['default_country_code']);
            
            updateSMSSetting($conn, 'sms_notifications_enabled', $sms_notifications_enabled);
            updateSMSSetting($conn, 'birthday_sms_enabled', $birthday_sms_enabled);
            updateSMSSetting($conn, 'event_sms_enabled', $event_sms_enabled);
            updateSMSSetting($conn, 'birthday_sms_template', $birthday_sms_template);
            updateSMSSetting($conn, 'event_sms_template', $event_sms_template);
            updateSMSSetting($conn, 'default_country_code', $default_country_code);
            
            $message = "General SMS ******** updated successfully.";
        }
        
        // Test SMS
        if (isset($_POST['send_test_sms'])) {
            $test_phone = trim($_POST['test_phone']);
            $test_message = trim($_POST['test_message']);
            $test_provider = trim($_POST['test_provider']);
            
            // Validate phone number
            if (empty($test_phone)) {
                throw new Exception("Phone number is required");
            }
            
            // Validate message
            if (empty($test_message)) {
                throw new Exception("Message is required");
            }
            
            // Here you would actually send the SMS using the selected provider
            // This is just a placeholder for the actual implementation
            
            $message = "Test SMS sent successfully to {$test_phone} using {$test_provider}.";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
        error_log("Error updating SMS ********: " . $e->getMessage());
    }
}

// Get current ********
$sms_******** = getSMS********($conn);

// Set default values if not set
$defaults = [
    'twilio_enabled' => '0',
    'twilio_account_sid' => '',
    'twilio_auth_token' => '',
    'twilio_phone_number' => '',
    'nexmo_enabled' => '0',
    'nexmo_api_key' => '',
    'nexmo_api_secret' => '',
    'nexmo_from' => '',
    'whatsapp_enabled' => '0',
    'whatsapp_api_key' => '',
    'whatsapp_api_secret' => '',
    'whatsapp_phone_number' => '',
    'sms_notifications_enabled' => '1',
    'birthday_sms_enabled' => '1',
    'event_sms_enabled' => '1',
    'birthday_sms_template' => 'Happy Birthday {name}! May God bless you abundantly on your special day. - Freedom Assembly Church',
    'event_sms_template' => 'Reminder: {event_name} on {event_date} at {event_time}. {event_location}. - Freedom Assembly Church',
    'default_country_code' => '+1'
];

foreach ($defaults as $key => $value) {
    if (!isset($sms_********[$key])) {
        $sms_********[$key] = $value;
    }
}

// Page title and header info
$page_title = "SMS Integration";
$page_header = "SMS Integration";
$page_description = "Configure SMS integration ******** for your church.";

// Include header
include 'includes/header.php';
?>
            
            <?php if (!empty($message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>General SMS ********</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="sms_notifications_enabled" name="sms_notifications_enabled" <?php echo $sms_********['sms_notifications_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="sms_notifications_enabled">Enable SMS Notifications</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="birthday_sms_enabled" name="birthday_sms_enabled" <?php echo $sms_********['birthday_sms_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="birthday_sms_enabled">Send Birthday SMS Messages</label>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="event_sms_enabled" name="event_sms_enabled" <?php echo $sms_********['event_sms_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="event_sms_enabled">Send Event Reminder SMS Messages</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="default_country_code" class="form-label">Default Country Code</label>
                                    <input type="text" class="form-control" id="default_country_code" name="default_country_code" value="<?php echo htmlspecialchars($sms_********['default_country_code']); ?>">
                                    <div class="form-text">Example: +1 for US, +44 for UK</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="birthday_sms_template" class="form-label">Birthday SMS Template</label>
                                    <textarea class="form-control" id="birthday_sms_template" name="birthday_sms_template" rows="3"><?php echo htmlspecialchars($sms_********['birthday_sms_template']); ?></textarea>
                                    <div class="form-text">Available placeholders: {name}, {age}, {date}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="event_sms_template" class="form-label">Event SMS Template</label>
                                    <textarea class="form-control" id="event_sms_template" name="event_sms_template" rows="3"><?php echo htmlspecialchars($sms_********['event_sms_template']); ?></textarea>
                                    <div class="form-text">Available placeholders: {event_name}, {event_date}, {event_time}, {event_location}</div>
                                </div>
                                
                                <button type="submit" name="general_sms_********" class="btn btn-primary">Save General ********</button>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Twilio Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="twilio_enabled" name="twilio_enabled" <?php echo $sms_********['twilio_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="twilio_enabled">Enable Twilio Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twilio_account_sid" class="form-label">Account SID</label>
                                    <input type="text" class="form-control" id="twilio_account_sid" name="twilio_account_sid" value="<?php echo htmlspecialchars($sms_********['twilio_account_sid']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twilio_auth_token" class="form-label">Auth Token</label>
                                    <input type="password" class="form-control" id="twilio_auth_token" name="twilio_auth_token" value="<?php echo htmlspecialchars($sms_********['twilio_auth_token']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="twilio_phone_number" class="form-label">Twilio Phone Number</label>
                                    <input type="text" class="form-control" id="twilio_phone_number" name="twilio_phone_number" value="<?php echo htmlspecialchars($sms_********['twilio_phone_number']); ?>">
                                    <div class="form-text">Include country code (e.g., +**********)</div>
                                </div>
                                
                                <button type="submit" name="twilio_********" class="btn btn-primary">Save Twilio ********</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Sign up for a <a href="https://www.twilio.com/" target="_blank">Twilio account</a></li>
                                    <li>Get your Account SID and Auth Token from the dashboard</li>
                                    <li>Purchase a phone number</li>
                                    <li>Enter the details above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Nexmo/Vonage Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="nexmo_enabled" name="nexmo_enabled" <?php echo $sms_********['nexmo_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="nexmo_enabled">Enable Nexmo/Vonage Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nexmo_api_key" class="form-label">API Key</label>
                                    <input type="text" class="form-control" id="nexmo_api_key" name="nexmo_api_key" value="<?php echo htmlspecialchars($sms_********['nexmo_api_key']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nexmo_api_secret" class="form-label">API Secret</label>
                                    <input type="password" class="form-control" id="nexmo_api_secret" name="nexmo_api_secret" value="<?php echo htmlspecialchars($sms_********['nexmo_api_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="nexmo_from" class="form-label">From (Sender ID)</label>
                                    <input type="text" class="form-control" id="nexmo_from" name="nexmo_from" value="<?php echo htmlspecialchars($sms_********['nexmo_from']); ?>">
                                    <div class="form-text">Alphanumeric sender ID or phone number</div>
                                </div>
                                
                                <button type="submit" name="nexmo_********" class="btn btn-primary">Save Nexmo ********</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Sign up for a <a href="https://www.vonage.com/" target="_blank">Vonage account</a></li>
                                    <li>Get your API Key and API Secret from the dashboard</li>
                                    <li>Set up a sender ID or purchase a phone number</li>
                                    <li>Enter the details above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>WhatsApp Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="whatsapp_enabled" name="whatsapp_enabled" <?php echo $sms_********['whatsapp_enabled'] == '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="whatsapp_enabled">Enable WhatsApp Integration</label>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="whatsapp_api_key" class="form-label">API Key</label>
                                    <input type="text" class="form-control" id="whatsapp_api_key" name="whatsapp_api_key" value="<?php echo htmlspecialchars($sms_********['whatsapp_api_key']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="whatsapp_api_secret" class="form-label">API Secret</label>
                                    <input type="password" class="form-control" id="whatsapp_api_secret" name="whatsapp_api_secret" value="<?php echo htmlspecialchars($sms_********['whatsapp_api_secret']); ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="whatsapp_phone_number" class="form-label">WhatsApp Phone Number</label>
                                    <input type="text" class="form-control" id="whatsapp_phone_number" name="whatsapp_phone_number" value="<?php echo htmlspecialchars($sms_********['whatsapp_phone_number']); ?>">
                                    <div class="form-text">Include country code (e.g., +**********)</div>
                                </div>
                                
                                <button type="submit" name="whatsapp_********" class="btn btn-primary">Save WhatsApp ********</button>
                            </form>
                            
                            <div class="mt-3">
                                <h6>Setup Instructions:</h6>
                                <ol class="small">
                                    <li>Sign up for <a href="https://www.twilio.com/whatsapp" target="_blank">Twilio WhatsApp API</a> or <a href="https://developers.facebook.com/docs/whatsapp/api/********" target="_blank">WhatsApp Business API</a></li>
                                    <li>Complete the verification process</li>
                                    <li>Get your API credentials</li>
                                    <li>Enter the details above</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Test SMS Integration</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" action="">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="test_provider" class="form-label">SMS Provider</label>
                                            <select class="form-select" id="test_provider" name="test_provider">
                                                <option value="twilio" <?php echo $sms_********['twilio_enabled'] == '1' ? '' : 'disabled'; ?>>Twilio</option>
                                                <option value="nexmo" <?php echo $sms_********['nexmo_enabled'] == '1' ? '' : 'disabled'; ?>>Nexmo/Vonage</option>
                                                <option value="whatsapp" <?php echo $sms_********['whatsapp_enabled'] == '1' ? '' : 'disabled'; ?>>WhatsApp</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="test_phone" class="form-label">Phone Number</label>
                                            <input type="text" class="form-control" id="test_phone" name="test_phone" placeholder="e.g., +**********">
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="test_message" class="form-label">Test Message</label>
                                            <input type="text" class="form-control" id="test_message" name="test_message" value="This is a test message from Freedom Assembly Church.">
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="submit" name="send_test_sms" class="btn btn-primary">Send Test Message</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>SMS Integration Preview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Birthday SMS Preview</h6>
                                    <div class="mb-4 p-3 border rounded">
                                        <div class="d-flex align-items-start">
                                            <div class="bg-light p-2 rounded me-2" style="max-width: 70%;">
                                                <p class="mb-0 small"><?php echo str_replace(
                                                    ['{name}', '{age}', '{date}'],
                                                    ['John Doe', '35', date('F j, Y')],
                                                    htmlspecialchars($sms_********['birthday_sms_template'])
                                                ); ?></p>
                                                <small class="text-muted">Sent via <?php echo $sms_********['twilio_enabled'] == '1' ? 'Twilio' : ($sms_********['nexmo_enabled'] == '1' ? 'Nexmo' : 'WhatsApp'); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Event SMS Preview</h6>
                                    <div class="mb-4 p-3 border rounded">
                                        <div class="d-flex align-items-start">
                                            <div class="bg-light p-2 rounded me-2" style="max-width: 70%;">
                                                <p class="mb-0 small"><?php echo str_replace(
                                                    ['{event_name}', '{event_date}', '{event_time}', '{event_location}'],
                                                    ['Sunday Service', 'Sunday, ' . date('F j, Y'), '10:00 AM', 'Main Sanctuary'],
                                                    htmlspecialchars($sms_********['event_sms_template'])
                                                ); ?></p>
                                                <small class="text-muted">Sent via <?php echo $sms_********['twilio_enabled'] == '1' ? 'Twilio' : ($sms_********['nexmo_enabled'] == '1' ? 'Nexmo' : 'WhatsApp'); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <p class="text-muted small">Note: The actual functionality depends on the ******** configured above.</p>
                        </div>
                    </div>
                </div>
            </div>


<?php include 'includes/footer.php'; ?> 
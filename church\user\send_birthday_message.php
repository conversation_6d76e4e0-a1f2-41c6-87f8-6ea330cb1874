<?php
/**
 * Send Birthday Message Page
 * 
 * Allows users to send personalized birthday messages using templates
 */

// Include configuration first (which sets up session configuration)
require_once '../config.php';

// Start session after configuration is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include classes
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    header("Location: login.php");
    exit();
}

$userId = $_SESSION['user_id'];
$userData = $userAuth->getUserById($userId);

if (!$userData) {
    session_destroy();
    header("Location: login.php");
    exit();
}

// Get site settings for branding
$sitename = 'Church Management System';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}

$error = '';
$success = '';
$selectedMember = null;
$selectedTemplate = null;

// Get member ID from URL parameter
$memberId = isset($_GET['member_id']) ? intval($_GET['member_id']) : 0;
$templateId = isset($_GET['template_id']) ? intval($_GET['template_id']) : 0;

// Get selected member if provided
if ($memberId) {
    $stmt = $pdo->prepare("
        SELECT id, full_name, first_name, last_name, email, image_path, birth_date
        FROM members 
        WHERE id = ? AND status = 'active' AND id != ?
    ");
    $stmt->execute([$memberId, $userId]);
    $selectedMember = $stmt->fetch();
}

// Get selected template if provided
if ($templateId) {
    $stmt = $pdo->prepare("
        SELECT
            id,
            template_name as name,
            subject as description,
            content as template_content,
            template_category as category
        FROM email_templates
        WHERE id = ? AND is_birthday_template = 1
    ");
    $stmt->execute([$templateId]);
    $selectedTemplate = $stmt->fetch();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $recipientId = intval($_POST['recipient_id']);
    $templateId = intval($_POST['template_id']);
    $customMessage = trim($_POST['custom_message'] ?? '');
    $deliveryMethod = $_POST['delivery_method'] ?? 'email';
    $scheduleDate = $_POST['schedule_date'] ?? date('Y-m-d');
    
    try {
        // Validate recipient
        $stmt = $pdo->prepare("
            SELECT id, full_name, first_name, email, phone_number
            FROM members 
            WHERE id = ? AND status = 'active' AND id != ?
        ");
        $stmt->execute([$recipientId, $userId]);
        $recipient = $stmt->fetch();
        
        if (!$recipient) {
            throw new Exception('Invalid recipient selected.');
        }
        
        // Validate template
        $stmt = $pdo->prepare("
            SELECT
                id,
                template_name as name,
                subject,
                content as template_content
            FROM email_templates
            WHERE id = ? AND is_birthday_template = 1
        ");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch();
        
        if (!$template) {
            throw new Exception('Invalid template selected.');
        }
        
        // Check if message already sent today for this recipient
        $stmt = $pdo->prepare("
            SELECT id FROM user_birthday_messages 
            WHERE sender_id = ? AND recipient_id = ? AND DATE(created_at) = CURDATE()
        ");
        $stmt->execute([$userId, $recipientId]);
        if ($stmt->fetch()) {
            throw new Exception('You have already sent a birthday message to this person today.');
        }
        
        // Insert birthday message record
        $stmt = $pdo->prepare("
            INSERT INTO user_birthday_messages 
            (sender_id, recipient_id, template_id, custom_message, scheduled_date, delivery_method, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $status = ($scheduleDate === date('Y-m-d')) ? 'sent' : 'scheduled';
        $stmt->execute([
            $userId, $recipientId, $templateId, $customMessage, 
            $scheduleDate, $deliveryMethod, $status
        ]);
        
        $messageId = $pdo->lastInsertId();
        
        // If sending immediately, process the email
        if ($status === 'sent') {
            // Get recipient's full member data including image
            $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$recipientId]);
            $fullRecipientData = $stmt->fetch();

            // Prepare site URL for image paths
            $siteUrl = defined('SITE_URL') ? SITE_URL :
                ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST']);

            // Handle member image
            $memberImageHtml = '';
            $memberImageUrl = '';

            if (!empty($fullRecipientData['image_path'])) {
                // Use member's profile image
                $memberImageUrl = $siteUrl . '/' . ltrim($fullRecipientData['image_path'], '/');
                $memberImageHtml = '<img src="' . $memberImageUrl . '" alt="' .
                    htmlspecialchars($recipient['full_name']) .
                    '" style="display:block; max-width:150px; height:auto; border-radius:50%; margin:15px auto;">';
            } else {
                // Use default profile image
                $memberImageUrl = $siteUrl . '/assets/img/default-profile.jpg';
                $memberImageHtml = '<img src="' . $memberImageUrl . '" alt="Profile Picture" ' .
                    'style="display:block; max-width:150px; height:auto; border-radius:50%; margin:15px auto;">';
            }

            // Prepare member data for placeholder replacement
            $memberData = [
                'full_name' => $recipient['full_name'],
                'first_name' => $recipient['first_name'],
                'last_name' => $recipient['last_name'] ?? '',
                'email' => $recipient['email'],
                'organization_name' => $sitename,
                'organization_type' => 'church',
                'sender_name' => $userData['first_name'],
                'sender_full_name' => $userData['first_name'] . ' ' . ($userData['last_name'] ?? ''),
                'church_name' => $sitename, // Legacy support
                'recipient_name' => $recipient['full_name'], // Legacy support

                // Member image placeholders
                'member_image' => $memberImageHtml,
                'member_image_url' => $memberImageUrl,
                'birthday_member_image' => $memberImageUrl,
                'birthday_member_photo_url' => $memberImageUrl
            ];

            // Use standardized placeholder replacement function
            $emailContent = replaceTemplatePlaceholders($template['template_content'], $memberData);

            // Add custom message if provided - use a specific placeholder approach
            if ($customMessage) {
                $customMessageHtml = '<div style="background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-left: 4px solid #667eea; border-radius: 5px;">
                    <h6 style="color: #667eea; margin-bottom: 10px;">Personal Message from ' . htmlspecialchars($userData['first_name']) . ':</h6>
                    <p style="margin: 0; font-style: italic;">' . nl2br(htmlspecialchars($customMessage)) . '</p>
                </div>';

                // Look for a specific placeholder first, otherwise append at the end
                if (strpos($emailContent, '{custom_message}') !== false) {
                    $emailContent = str_replace('{custom_message}', $customMessageHtml, $emailContent);
                } else {
                    // Append before the last closing tag
                    $emailContent = preg_replace('/(<\/div>\s*<\/div>\s*)$/i', $customMessageHtml . '$1', $emailContent);
                }
            } else {
                // Remove custom message placeholder if no message provided
                $emailContent = str_replace('{custom_message}', '', $emailContent);
            }
            
            // Use template subject with placeholder replacement
            $subject = replaceTemplatePlaceholders($template['subject'], $memberData);

            // Fallback to default subject if template subject is empty
            if (empty($subject)) {
                $subject = "🎉 Happy Birthday " . $recipient['first_name'] . "! 🎂";
            }
            
            if (sendEmail($recipient['email'], $recipient['full_name'], $subject, $emailContent, true)) {
                // Update message status
                $stmt = $pdo->prepare("UPDATE user_birthday_messages SET sent_at = NOW() WHERE id = ?");
                $stmt->execute([$messageId]);
                
                // Update template usage count in tracking table
                $stmt = $pdo->prepare("
                    INSERT INTO email_template_usage (template_id, template_type, used_by)
                    VALUES (?, 'birthday', ?)
                ");
                $stmt->execute([$templateId, $userId]);
                
                // Log user activity
                $userAuth->logUserActivity(
                    $userId, 
                    'birthday_message_sent', 
                    "Sent birthday message to {$recipient['full_name']}", 
                    'birthday_message', 
                    $messageId,
                    ['recipient_id' => $recipientId, 'template_id' => $templateId]
                );
                
                $success = "Birthday message sent successfully to {$recipient['full_name']}!";
            } else {
                // Update status to failed
                $stmt = $pdo->prepare("UPDATE user_birthday_messages SET status = 'failed' WHERE id = ?");
                $stmt->execute([$messageId]);
                
                throw new Exception('Failed to send email. Please try again.');
            }
        } else {
            $success = "Birthday message scheduled for " . date('M j, Y', strtotime($scheduleDate)) . "!";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all active members (excluding current user)
$members = [];
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, email, image_path, birth_date
    FROM members 
    WHERE status = 'active' AND id != ?
    ORDER BY full_name
");
$stmt->execute([$userId]);
$members = $stmt->fetchAll();

// Get birthday templates from admin-created email templates
$templates = [];
$stmt = $pdo->prepare("
    SELECT
        id,
        template_name as name,
        subject as description,
        template_category as category
    FROM email_templates
    WHERE is_birthday_template = 1
    ORDER BY template_category, template_name
");
$stmt->execute();
$templates = $stmt->fetchAll();

// Get today's birthdays for suggestions
$todaysBirthdays = [];
$stmt = $pdo->prepare("
    SELECT id, full_name, first_name, image_path
    FROM members 
    WHERE status = 'active' 
    AND id != ? 
    AND MONTH(birth_date) = MONTH(CURDATE()) 
    AND DAY(birth_date) = DAY(CURDATE())
    ORDER BY full_name
");
$stmt->execute([$userId]);
$todaysBirthdays = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Send Birthday Message - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom Theme CSS from Appearance Settings -->
    <?php include 'includes/theme_css.php'; ?>

    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .form-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid var(--bs-border-color, #e9ecef);
        }

        .member-suggestion {
            background: var(--bs-body-bg, white);
            border: 2px solid var(--bs-border-color, #e9ecef);
            border-radius: var(--bs-border-radius, 10px);
            padding: 1rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .member-suggestion:hover {
            border-color: var(--bs-primary, #667eea);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(var(--bs-primary-rgb, 102, 126, 234), 0.1);
        }

        .member-suggestion.selected {
            border-color: var(--bs-primary, #667eea);
            background-color: rgba(var(--bs-primary-rgb, 102, 126, 234), 0.05);
        }

        .template-preview {
            background-color: var(--bs-light, #f8f9fa);
            border-radius: var(--bs-border-radius, 10px);
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--bs-border-color, #e9ecef);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            border-radius: var(--bs-border-radius, 10px);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }

        .btn-outline-primary {
            border-color: var(--bs-primary, #667eea);
            color: var(--bs-primary, #667eea);
            border-radius: var(--bs-border-radius, 10px);
            font-weight: 600;
        }

        .birthday-suggestions {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="bi bi-house-heart"></i> <?php echo htmlspecialchars($sitename); ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="bi bi-calendar-event"></i> Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="birthday_templates.php">
                            <i class="bi bi-gift"></i> Birthdays
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <?php echo htmlspecialchars($userData['first_name'] ?: $userData['full_name']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="bi bi-person"></i> My Profile</a></li>
                            <li><a class="dropdown-item" href="change_password.php"><i class="bi bi-shield-lock"></i> Change Password</a></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="bi bi-gear"></i> Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php"><i class="bi bi-box-arrow-right"></i> Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex align-items-center">
                    <a href="birthday_templates.php" class="btn btn-outline-primary me-3">
                        <i class="bi bi-arrow-left"></i> Back to Templates
                    </a>
                    <div>
                        <h2><i class="bi bi-envelope-heart"></i> Send Birthday Message</h2>
                        <p class="text-muted mb-0">Send a personalized birthday message to a church member.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($success): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Today's Birthday Suggestions -->
        <?php if (!empty($todaysBirthdays) && !$selectedMember): ?>
        <div class="birthday-suggestions">
            <h5><i class="bi bi-calendar-heart"></i> Today's Birthday Celebrations</h5>
            <p class="mb-3">These members are celebrating their birthday today! Click to send them wishes:</p>
            <div class="d-flex flex-wrap gap-2">
                <?php foreach ($todaysBirthdays as $birthday): ?>
                <button class="btn btn-light btn-sm" onclick="selectMember(<?php echo $birthday['id']; ?>, '<?php echo htmlspecialchars($birthday['full_name'], ENT_QUOTES); ?>')">
                    <i class="bi bi-gift"></i> <?php echo htmlspecialchars($birthday['full_name']); ?>
                </button>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Send Message Form -->
        <div class="form-card">
            <form method="POST" action="">
                <div class="row">
                    <!-- Recipient Selection -->
                    <div class="col-md-6 mb-4">
                        <h5><i class="bi bi-person-heart"></i> Select Recipient</h5>
                        
                        <div class="mb-3">
                            <label for="recipient_search" class="form-label">Search Members</label>
                            <input type="text" class="form-control" id="recipient_search" placeholder="Type to search members...">
                        </div>
                        
                        <div id="member_list" style="max-height: 300px; overflow-y: auto;">
                            <?php foreach ($members as $member): ?>
                            <div class="member-suggestion" 
                                 data-member-id="<?php echo $member['id']; ?>"
                                 data-member-name="<?php echo htmlspecialchars($member['full_name']); ?>"
                                 onclick="selectMember(<?php echo $member['id']; ?>, '<?php echo htmlspecialchars($member['full_name'], ENT_QUOTES); ?>')"
                                 <?php echo ($selectedMember && $selectedMember['id'] == $member['id']) ? 'style="display: block;" class="member-suggestion selected"' : ''; ?>>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <?php if (!empty($member['image_path'])): ?>
                                            <img src="../<?php echo htmlspecialchars($member['image_path']); ?>" 
                                                 alt="<?php echo htmlspecialchars($member['full_name']); ?>" 
                                                 class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="rounded-circle bg-secondary d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px;">
                                                <i class="bi bi-person-fill text-white"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($member['full_name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($member['email']); ?></small>
                                        <?php if ($member['birth_date']): ?>
                                        <br><small class="text-muted">
                                            Birthday: <?php echo date('M j', strtotime($member['birth_date'])); ?>
                                        </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <input type="hidden" name="recipient_id" id="recipient_id" value="<?php echo $selectedMember ? $selectedMember['id'] : ''; ?>" required>
                        <div id="selected_recipient" class="mt-3" <?php echo !$selectedMember ? 'style="display: none;"' : ''; ?>>
                            <div class="alert alert-info">
                                <strong>Selected:</strong> <span id="selected_recipient_name"><?php echo $selectedMember ? htmlspecialchars($selectedMember['full_name']) : ''; ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Template Selection -->
                    <div class="col-md-6 mb-4">
                        <h5><i class="bi bi-palette"></i> Choose Template</h5>
                        
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Birthday Template</label>
                            <select class="form-select" name="template_id" id="template_id" required onchange="previewTemplate()">
                                <option value="">Select a template...</option>
                                <?php
                                $currentCategory = '';
                                foreach ($templates as $template):
                                    if ($template['category'] !== $currentCategory):
                                        if ($currentCategory !== '') echo '</optgroup>';
                                        echo '<optgroup label="' . ucfirst($template['category']) . '">';
                                        $currentCategory = $template['category'];
                                    endif;
                                ?>
                                <option value="<?php echo $template['id']; ?>" 
                                        <?php echo ($selectedTemplate && $selectedTemplate['id'] == $template['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($template['name']); ?>
                                </option>
                                <?php endforeach; ?>
                                <?php if ($currentCategory !== '') echo '</optgroup>'; ?>
                            </select>
                        </div>
                        
                        <div id="template_preview" class="template-preview" style="display: none;">
                            <!-- Template preview will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Custom Message -->
                <div class="mb-4">
                    <h5><i class="bi bi-chat-heart"></i> Personal Message (Optional)</h5>
                    <div class="mb-3">
                        <label for="custom_message" class="form-label">Add your personal touch</label>
                        <textarea class="form-control" name="custom_message" id="custom_message" rows="4" 
                                  placeholder="Add a personal message that will be included with the template..."></textarea>
                        <div class="form-text">This message will be highlighted in the email as a personal note from you.</div>
                    </div>
                </div>

                <!-- Delivery Options -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5><i class="bi bi-send"></i> Delivery Options</h5>
                        <div class="mb-3">
                            <label for="delivery_method" class="form-label">Delivery Method</label>
                            <select class="form-select" name="delivery_method" id="delivery_method">
                                <option value="email">Email</option>
                                <!-- SMS option can be added later -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="bi bi-calendar-check"></i> Schedule</h5>
                        <div class="mb-3">
                            <label for="schedule_date" class="form-label">Send Date</label>
                            <input type="date" class="form-control" name="schedule_date" id="schedule_date" 
                                   value="<?php echo date('Y-m-d'); ?>" min="<?php echo date('Y-m-d'); ?>">
                            <div class="form-text">Choose today to send immediately, or schedule for a future date.</div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-send"></i> Send Birthday Message
                    </button>
                    <a href="birthday_templates.php" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Member search functionality
        document.getElementById('recipient_search').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const memberSuggestions = document.querySelectorAll('.member-suggestion');
            
            memberSuggestions.forEach(function(suggestion) {
                const memberName = suggestion.dataset.memberName.toLowerCase();
                if (memberName.includes(searchTerm)) {
                    suggestion.style.display = 'block';
                } else {
                    suggestion.style.display = 'none';
                }
            });
        });

        // Member selection
        function selectMember(memberId, memberName) {
            // Clear previous selections
            document.querySelectorAll('.member-suggestion').forEach(function(el) {
                el.classList.remove('selected');
            });
            
            // Select current member
            document.querySelector(`[data-member-id="${memberId}"]`).classList.add('selected');
            
            // Update form
            document.getElementById('recipient_id').value = memberId;
            document.getElementById('selected_recipient_name').textContent = memberName;
            document.getElementById('selected_recipient').style.display = 'block';
            
            // Update template preview if template is selected
            previewTemplate();
        }

        // Template preview
        function previewTemplate() {
            const templateId = document.getElementById('template_id').value;
            const recipientId = document.getElementById('recipient_id').value;
            
            if (templateId && recipientId) {
                fetch(`ajax/preview_birthday_template.php?template_id=${templateId}&recipient_id=${recipientId}`)
                    .then(response => response.text())
                    .then(html => {
                        document.getElementById('template_preview').innerHTML = html;
                        document.getElementById('template_preview').style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error loading template preview:', error);
                    });
            } else {
                document.getElementById('template_preview').style.display = 'none';
            }
        }

        // Initialize template preview if both are pre-selected
        <?php if ($selectedTemplate && $selectedMember): ?>
        previewTemplate();
        <?php endif; ?>

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>

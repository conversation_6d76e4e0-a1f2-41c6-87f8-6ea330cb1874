<?php
require_once 'config.php';

echo "<h2>Email Templates Schema Check</h2>";

try {
    $stmt = $pdo->query("DESCRIBE email_templates");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($columns) {
        echo "<p style='color: green;'>✓ Email templates table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check existing templates
        echo "<h3>Existing Templates</h3>";
        $stmt = $pdo->query("SELECT id, template_name, subject FROM email_templates ORDER BY id");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($templates) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Template Name</th><th>Subject</th></tr>";
            foreach ($templates as $template) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($template['id']) . "</td>";
                echo "<td>" . htmlspecialchars($template['template_name']) . "</td>";
                echo "<td>" . htmlspecialchars($template['subject']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No templates found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Email templates table not found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

?>

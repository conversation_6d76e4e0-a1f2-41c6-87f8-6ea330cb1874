<?php
require_once 'config.php';

echo "<h2>Registration Email Test</h2>";

// Test member data
$testMemberData = [
    'full_name' => 'Test User Email Fix',
    'first_name' => 'Test',
    'last_name' => 'User Email Fix',
    'email' => '<EMAIL>',
    'phone_number' => '******-123-4567',
    'home_address' => '123 Test Street, Test City, TC 12345',
    'occupation' => 'Software Tester',
    'image_path' => ''
];

echo "<h3>1. Email Settings Check</h3>";
if (!empty($emailSettings)) {
    echo "<p style='color: green;'>✓ Email settings loaded successfully</p>";
    
    // Check required settings
    $required_settings = ['smtp_host', 'smtp_auth', 'smtp_username', 'smtp_password', 
                         'smtp_secure', 'smtp_port', 'sender_email', 'sender_name'];
    $missing_settings = array_diff($required_settings, array_keys($emailSettings));
    
    if (empty($missing_settings)) {
        echo "<p style='color: green;'>✓ All required email settings present</p>";
    } else {
        echo "<p style='color: red;'>❌ Missing email settings: " . implode(', ', $missing_settings) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Email settings not loaded</p>";
}

echo "<h3>2. Welcome Email Test</h3>";
echo "<p>Testing sendWelcomeEmail function with test data:</p>";
echo "<pre>" . print_r($testMemberData, true) . "</pre>";

try {
    // Test the sendWelcomeEmail function
    $emailResult = sendWelcomeEmail($testMemberData);
    
    if ($emailResult) {
        echo "<p style='color: green;'>✓ Welcome email sent successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Welcome email failed to send</p>";
        
        // Check for last email error
        global $last_email_error;
        if ($last_email_error) {
            echo "<p style='color: red;'>Error details: " . htmlspecialchars($last_email_error) . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception occurred: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>3. Email Debug Log</h3>";
$debug_log_file = __DIR__ . '/logs/email_debug.log';
if (file_exists($debug_log_file)) {
    echo "<p>Last 10 lines from email debug log:</p>";
    // Use tail command or read file in chunks to avoid memory issues
    $file_size = filesize($debug_log_file);
    if ($file_size > 1024 * 1024) { // If file is larger than 1MB
        echo "<p style='color: orange;'>⚠ Log file is large (" . round($file_size / 1024 / 1024, 2) . " MB). Showing recent entries only.</p>";
        // Read last 2KB of file
        $handle = fopen($debug_log_file, 'r');
        fseek($handle, max(0, $file_size - 2048));
        $content = fread($handle, 2048);
        fclose($handle);
        $lines = explode("\n", $content);
        $recent_lines = array_slice($lines, -10);
    } else {
        $log_lines = file($debug_log_file);
        $recent_lines = array_slice($log_lines, -10);
    }
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(implode("\n", $recent_lines));
    echo "</pre>";
} else {
    echo "<p style='color: orange;'>⚠ Email debug log file not found</p>";
}

echo "<h3>4. Registration Process Simulation</h3>";
echo "<p>Simulating the registration process flow:</p>";

// Simulate the registration process
try {
    // Check if email already exists (to avoid duplicate entry error)
    $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
    $stmt->execute([$testMemberData['email']]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "<p style='color: orange;'>⚠ Test email already exists in database (ID: {$existing['id']})</p>";
        echo "<p>Testing welcome email for existing user...</p>";
        
        // Test welcome email for existing user
        $emailResult = sendWelcomeEmail($testMemberData);
        
        if ($emailResult) {
            echo "<p style='color: green;'>✓ Welcome email would be sent successfully for new registration!</p>";
        } else {
            echo "<p style='color: red;'>❌ Welcome email would fail for new registration</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ Test email not in database - this is good for testing</p>";
        echo "<p>Testing welcome email function...</p>";
        
        // Test welcome email function
        $emailResult = sendWelcomeEmail($testMemberData);
        
        if ($emailResult) {
            echo "<p style='color: green;'>✓ Welcome email function works correctly!</p>";
        } else {
            echo "<p style='color: red;'>❌ Welcome email function has issues</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h3>5. Conclusion</h3>";
if (!empty($emailSettings) && function_exists('sendWelcomeEmail')) {
    echo "<p style='color: green;'>✓ Email configuration appears to be fixed!</p>";
    echo "<p>The registration email issue should now be resolved. Users should receive welcome emails upon successful registration.</p>";
} else {
    echo "<p style='color: red;'>❌ Email configuration still has issues</p>";
}

?>

<?php
/**
 * Theme CSS Include for User Pages
 * 
 * This file includes the custom theme CSS generated by admin appearance settings
 * Include this file in the <head> section of user pages after Bootstrap CSS
 */

// Load custom theme CSS if it exists
$customThemeFile = '../admin/css/custom-theme.css';
if (file_exists($customThemeFile)): ?>
    <link rel="stylesheet" href="<?php echo '../admin/css/custom-theme.css?t=' . filemtime($customThemeFile); ?>">
<?php endif; ?>

<!-- Also check for cached theme CSS -->
<?php
$cachedThemeFile = '../cache/theme-cache.css';
if (file_exists($cachedThemeFile) && !file_exists($customThemeFile)): ?>
    <link rel="stylesheet" href="<?php echo '../cache/theme-cache.css?t=' . filemtime($cachedThemeFile); ?>">
<?php endif; ?>

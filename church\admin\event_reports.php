<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection - using the connection from config.php
$conn = $pdo;

// Define functions first
function generateEventReport($type, $date_from, $date_to, $event_id = null) {
    global $pdo;

    // Build query based on report type
    $where_conditions = [];
    $params = [];

    if ($date_from) {
        $where_conditions[] = "e.event_date >= ?";
        $params[] = $date_from;
    }

    if ($date_to) {
        $where_conditions[] = "e.event_date <= ?";
        $params[] = $date_to . ' 23:59:59';
    }

    if ($event_id) {
        $where_conditions[] = "e.id = ?";
        $params[] = $event_id;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    if ($type === 'attendance') {
        // Attendance report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees,
                   COUNT(er.id) as total_rsvps,
                   SUM(CASE WHEN er.status = 'attending' THEN 1 ELSE 0 END) as attending_count,
                   SUM(CASE WHEN er.status = 'not_attending' THEN 1 ELSE 0 END) as not_attending_count,
                   SUM(CASE WHEN er.status = 'maybe' THEN 1 ELSE 0 END) as maybe_count
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    } else {
        // Event summary report
        $sql = "
            SELECT e.title, e.event_date, e.location, e.max_attendees, e.is_active,
                   COUNT(er.id) as total_rsvps
            FROM events e
            LEFT JOIN event_rsvps er ON e.id = er.event_id
            $where_clause
            GROUP BY e.id
            ORDER BY e.event_date DESC
        ";
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Generate PDF
    generatePDFReport($data, $type, $date_from, $date_to);
}

// Handle report generation BEFORE any output
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'generate_report':
            $report_type = $_POST['report_type'];
            $date_from = $_POST['date_from'];
            $date_to = $_POST['date_to'];
            $event_id = !empty($_POST['event_id']) ? (int)$_POST['event_id'] : null;

            // Generate and download the report
            generateEventReport($report_type, $date_from, $date_to, $event_id);
            exit(); // Important: exit after generating report
            break;
    }
}

// Set page variables
$page_title = "Event Reports";
$page_header = "Event Reports";
$page_description = "Generate and download event attendance reports.";

// Include header
include 'includes/header.php';

// Get events for dropdown
$events_stmt = $pdo->query("SELECT id, title, event_date FROM events ORDER BY event_date DESC");
$events = $events_stmt->fetchAll(PDO::FETCH_ASSOC);

function generatePDFReport($data, $type, $date_from, $date_to) {
    // Clean output buffer to prevent header issues
    if (ob_get_level()) {
        ob_end_clean();
    }

    $title = ucfirst($type) . " Report";
    $date_range = "";
    if ($date_from || $date_to) {
        $date_range = " (" . ($date_from ?: 'Start') . " to " . ($date_to ?: 'End') . ")";
    }

    header('Content-Type: text/html; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>' . htmlspecialchars($title) . '</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            * { box-sizing: border-box; }
            body {
                font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: #fff;
                color: #333;
                line-height: 1.6;
            }
            .container { max-width: 1200px; margin: 0 auto; }
            .header {
                text-align: center;
                margin-bottom: 40px;
                padding-bottom: 20px;
                border-bottom: 3px solid #007bff;
            }
            .header h1 {
                color: #007bff;
                margin: 0 0 10px 0;
                font-size: 2.2em;
                font-weight: 300;
            }
            .header h2 {
                color: #333;
                margin: 0 0 10px 0;
                font-size: 1.5em;
                font-weight: 400;
            }
            .header .meta {
                color: #666;
                font-size: 0.9em;
                margin-top: 15px;
            }
            .controls {
                margin-bottom: 30px;
                text-align: center;
                padding: 15px;
                background: #f8f9fa;
                border-radius: 8px;
            }
            .controls button {
                background: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                margin: 0 10px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s ease;
            }
            .controls button:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }
            .controls button.secondary {
                background: #6c757d;
            }
            .controls button.secondary:hover {
                background: #545b62;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
                background: white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                border-radius: 8px;
                overflow: hidden;
            }
            th, td {
                padding: 12px 15px;
                text-align: left;
                border-bottom: 1px solid #e9ecef;
            }
            th {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                font-weight: 600;
                text-transform: uppercase;
                font-size: 0.85em;
                letter-spacing: 0.5px;
            }
            tr:nth-child(even) { background-color: #f8f9fa; }
            tr:hover { background-color: #e3f2fd; }
            .summary-stats {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .stat-card {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                border-left: 4px solid #007bff;
            }
            .stat-number {
                font-size: 2em;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 5px;
            }
            .stat-label {
                color: #666;
                font-size: 0.9em;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            @media print {
                .no-print { display: none !important; }
                body { margin: 0; padding: 15px; }
                .header { border-bottom: 2px solid #333; }
                table { box-shadow: none; }
                tr:hover { background-color: transparent !important; }
            }
            @media (max-width: 768px) {
                .container { padding: 10px; }
                table { font-size: 0.9em; }
                th, td { padding: 8px 10px; }
                .controls button { padding: 10px 16px; margin: 5px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . htmlspecialchars(get_organization_name()) . '</h1>
                <h2>' . htmlspecialchars($title . $date_range) . '</h2>
                <div class="meta">
                    <strong>Generated on:</strong> ' . date('F j, Y \a\t g:i A') . '<br>
                    <strong>Total Records:</strong> ' . count($data) . '
                </div>
            </div>

            <div class="controls no-print">
                <button onclick="window.print()" title="Print or Save as PDF">
                    📄 Print/Save as PDF
                </button>
                <button onclick="closeWindow()" class="secondary" title="Close this window">
                    ✕ Close
                </button>
            </div>';

            <!-- Summary Statistics -->
            <div class="summary-stats no-print">';

    // Calculate summary statistics
    $totalEvents = count($data);
    $totalRSVPs = array_sum(array_column($data, 'total_rsvps'));
    $totalAttending = 0;
    $totalCapacity = 0;

    foreach ($data as $row) {
        if (isset($row['attending_count'])) {
            $totalAttending += $row['attending_count'];
        }
        if (isset($row['max_attendees']) && is_numeric($row['max_attendees'])) {
            $totalCapacity += $row['max_attendees'];
        }
    }

    echo '
                <div class="stat-card">
                    <div class="stat-number">' . $totalEvents . '</div>
                    <div class="stat-label">Total Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">' . $totalRSVPs . '</div>
                    <div class="stat-label">Total RSVPs</div>
                </div>';

    if ($type === 'attendance') {
        echo '
                <div class="stat-card">
                    <div class="stat-number">' . $totalAttending . '</div>
                    <div class="stat-label">Total Attending</div>
                </div>';

        if ($totalCapacity > 0) {
            $utilizationRate = round(($totalAttending / $totalCapacity) * 100, 1);
            echo '
                <div class="stat-card">
                    <div class="stat-number">' . $utilizationRate . '%</div>
                    <div class="stat-label">Capacity Utilization</div>
                </div>';
        }
    }

    echo '
            </div>

            <table>
                <thead>
                    <tr>';

    if ($type === 'attendance') {
        echo '<th>Event Name</th>
              <th>Date & Time</th>
              <th>Location</th>
              <th>Capacity</th>
              <th>Total RSVPs</th>
              <th>✅ Attending</th>
              <th>❌ Not Attending</th>
              <th>❓ Maybe</th>
              <th>Utilization</th>';
    } else {
        echo '<th>Event Name</th>
              <th>Date & Time</th>
              <th>Location</th>
              <th>Capacity</th>
              <th>Status</th>
              <th>Total RSVPs</th>';
    }

    echo '</tr>
                </thead>
                <tbody>';

    if (empty($data)) {
        $colspan = ($type === 'attendance') ? 9 : 6;
        echo '<tr><td colspan="' . $colspan . '" style="text-align: center; padding: 40px; color: #666; font-style: italic;">
                No events found for the selected criteria.
              </td></tr>';
    } else {
        foreach ($data as $row) {
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($row['title']) . '</strong></td>';
            echo '<td>' . date('M j, Y<br>g:i A', strtotime($row['event_date'])) . '</td>';
            echo '<td>' . htmlspecialchars($row['location'] ?? 'TBD') . '</td>';

            $capacity = $row['max_attendees'] ?? null;
            echo '<td>' . ($capacity ? number_format($capacity) : 'Unlimited') . '</td>';

            if ($type === 'attendance') {
                $attending = $row['attending_count'] ?? 0;
                $notAttending = $row['not_attending_count'] ?? 0;
                $maybe = $row['maybe_count'] ?? 0;
                $totalRsvp = $row['total_rsvps'] ?? 0;

                echo '<td><strong>' . number_format($totalRsvp) . '</strong></td>';
                echo '<td style="color: #28a745;"><strong>' . number_format($attending) . '</strong></td>';
                echo '<td style="color: #dc3545;">' . number_format($notAttending) . '</td>';
                echo '<td style="color: #ffc107;">' . number_format($maybe) . '</td>';

                // Calculate utilization rate
                if ($capacity && $capacity > 0) {
                    $utilization = round(($attending / $capacity) * 100, 1);
                    $utilizationColor = $utilization >= 80 ? '#28a745' : ($utilization >= 50 ? '#ffc107' : '#dc3545');
                    echo '<td style="color: ' . $utilizationColor . ';"><strong>' . $utilization . '%</strong></td>';
                } else {
                    echo '<td style="color: #6c757d;">N/A</td>';
                }
            } else {
                $status = $row['is_active'] ?? true;
                $statusColor = $status ? '#28a745' : '#dc3545';
                $statusText = $status ? 'Active' : 'Inactive';
                echo '<td style="color: ' . $statusColor . ';"><strong>' . $statusText . '</strong></td>';
                echo '<td><strong>' . number_format($row['total_rsvps'] ?? 0) . '</strong></td>';
            }

            echo '</tr>';
        }
    }

    echo '</tbody>
            </table>

            <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px; font-size: 0.9em; color: #666;">
                <p><strong>Report Notes:</strong></p>
                <ul style="margin: 10px 0;">
                    <li>This report was generated automatically from the Church Campaign Management System</li>
                    <li>Data is current as of the generation timestamp shown above</li>
                    <li>RSVP counts include all response types (Attending, Not Attending, Maybe)</li>';

    if ($type === 'attendance') {
        echo '<li>Capacity utilization is calculated as (Attending / Capacity) × 100%</li>';
    }

    echo '      </ul>
            </div>
        </div>

        <script>
            function closeWindow() {
                if (window.opener) {
                    window.close();
                } else {
                    // If not opened in popup, go back to reports page
                    window.location.href = "event_reports.php";
                }
            }

            // Auto-focus for better user experience
            document.addEventListener("DOMContentLoaded", function() {
                // Add keyboard shortcuts
                document.addEventListener("keydown", function(e) {
                    if (e.ctrlKey && e.key === "p") {
                        e.preventDefault();
                        window.print();
                    }
                    if (e.key === "Escape") {
                        closeWindow();
                    }
                });
            });
        </script>
    </body>
    </html>';

    exit();
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Generate Event Reports</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="" id="reportForm" target="_blank">
                        <input type="hidden" name="action" value="generate_report">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="report_type" class="form-label">Report Type</label>
                                    <select class="form-select" id="report_type" name="report_type" required>
                                        <option value="">Select Report Type</option>
                                        <option value="attendance">Attendance Report</option>
                                        <option value="summary">Event Summary Report</option>
                                    </select>
                                    <div class="form-text">Choose the type of report you want to generate</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="event_id" class="form-label">Specific Event (Optional)</label>
                                    <select class="form-select" id="event_id" name="event_id">
                                        <option value="">All Events</option>
                                        <?php foreach ($events as $event): ?>
                                            <option value="<?= $event['id'] ?>">
                                                <?= htmlspecialchars($event['title']) ?> - <?= date('M j, Y', strtotime($event['event_date'])) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Leave blank to include all events</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_from" class="form-label">From Date</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from">
                                    <div class="form-text">Start date for the report range</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date_to" class="form-label">To Date</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to">
                                    <div class="form-text">End date for the report range</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-file-earmark-pdf"></i> Generate Report
                                </button>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        The report will open in a new window where you can print or save as PDF.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('reportForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;

    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating Report...';

        // Re-enable button after a delay
        setTimeout(function() {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }, 3000);
    });

    // Set default date range to current month
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('date_from').value = firstDay.toISOString().split('T')[0];
    document.getElementById('date_to').value = lastDay.toISOString().split('T')[0];

    // Update form help text based on report type
    document.getElementById('report_type').addEventListener('change', function() {
        const helpTexts = {
            'attendance': 'Detailed attendance breakdown showing who is attending, not attending, or maybe attending each event.',
            'summary': 'High-level overview of events with basic statistics and status information.'
        };

        const existingHelp = document.querySelector('.report-type-help');
        if (existingHelp) {
            existingHelp.remove();
        }

        if (this.value && helpTexts[this.value]) {
            const helpDiv = document.createElement('div');
            helpDiv.className = 'alert alert-info mt-2 report-type-help';
            helpDiv.innerHTML = '<i class="bi bi-info-circle"></i> ' + helpTexts[this.value];
            this.closest('.mb-3').appendChild(helpDiv);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
